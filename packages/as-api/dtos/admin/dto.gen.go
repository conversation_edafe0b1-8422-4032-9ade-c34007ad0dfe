// Package dtos provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/deepmap/oapi-codegen/v2 version v2.1.0 DO NOT EDIT.
package dtos

import (
	"encoding/json"
	"time"

	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerAuthScopes = "bearerAuth.Scopes"
)

// Defines values for AccountStatus.
const (
	AccountStatusActive     AccountStatus = "active"
	AccountStatusInreview   AccountStatus = "inreview"
	AccountStatusRestricted AccountStatus = "restricted"
	AccountStatusSuspended  AccountStatus = "suspended"
	AccountStatusViewonly   AccountStatus = "viewonly"
)

// Defines values for AccountType.
const (
	AccountTypePremium  AccountType = "premium"
	AccountTypeSeller   AccountType = "seller"
	AccountTypeStandard AccountType = "standard"
)

// Defines values for AuthRequestGrantType.
const (
	Password     AuthRequestGrantType = "password"
	RefreshToken AuthRequestGrantType = "refreshToken"
)

// Defines values for BankAccountAccountType.
const (
	Checking  BankAccountAccountType = "checking"
	Corporate BankAccountAccountType = "corporate"
	Savings   BankAccountAccountType = "savings"
)

// Defines values for BankAccountType.
const (
	IBAN         BankAccountType = "IBAN"
	JAPANACCOUNT BankAccountType = "JAPAN_ACCOUNT"
	SWIFTCODE    BankAccountType = "SWIFT_CODE"
	UAEACCOUNT   BankAccountType = "UAE_ACCOUNT"
)

// Defines values for BannerActionType.
const (
	InAppUrl BannerActionType = "inAppUrl"
	NoAction BannerActionType = "noAction"
	Query    BannerActionType = "query"
	Shop     BannerActionType = "shop"
	Url      BannerActionType = "url"
)

// Defines values for BannerStatus.
const (
	BannerStatusDraft     BannerStatus = "draft"
	BannerStatusPublished BannerStatus = "published"
)

// Defines values for ContentStatus.
const (
	ContentStatusDraft     ContentStatus = "draft"
	ContentStatusPublished ContentStatus = "published"
)

// Defines values for CurrencyCode.
const (
	AED CurrencyCode = "AED"
	AUD CurrencyCode = "AUD"
	EUR CurrencyCode = "EUR"
	GBP CurrencyCode = "GBP"
	HKD CurrencyCode = "HKD"
	JPY CurrencyCode = "JPY"
	KRW CurrencyCode = "KRW"
	SAR CurrencyCode = "SAR"
	SGD CurrencyCode = "SGD"
	USD CurrencyCode = "USD"
)

// Defines values for KycKycType.
const (
	Driverlicense KycKycType = "driverlicense"
	Passport      KycKycType = "passport"
)

// Defines values for MasterStatus.
const (
	MasterStatusDraft            MasterStatus = "draft"
	MasterStatusPublished        MasterStatus = "published"
	MasterStatusWaitingToPublish MasterStatus = "waitingToPublish"
)

// Defines values for NotificationSettingsType.
const (
	All       NotificationSettingsType = "all"
	Campaigns NotificationSettingsType = "campaigns"
	Important NotificationSettingsType = "important"
)

// Defines values for NotificationType.
const (
	CAMPAIGNPROMOTION NotificationType = "CAMPAIGN_PROMOTION"
	IMPORTANTSYSTEM   NotificationType = "IMPORTANT_SYSTEM"
)

// Defines values for OrderBy.
const (
	Asc  OrderBy = "asc"
	Desc OrderBy = "desc"
)

// Defines values for ProductCondition.
const (
	ProductConditionLevel1 ProductCondition = "level1"
	ProductConditionLevel2 ProductCondition = "level2"
	ProductConditionLevel3 ProductCondition = "level3"
	ProductConditionLevel4 ProductCondition = "level4"
	ProductConditionLevel5 ProductCondition = "level5"
	ProductConditionLevel6 ProductCondition = "level6"
)

// Defines values for ProductSalesStatus.
const (
	ProductSalesStatusAvailable ProductSalesStatus = "available"
	ProductSalesStatusDraft     ProductSalesStatus = "draft"
	ProductSalesStatusOnSale    ProductSalesStatus = "onSale"
	ProductSalesStatusSold      ProductSalesStatus = "sold"
	ProductSalesStatusSuspended ProductSalesStatus = "suspended"
)

// Defines values for ProductTarget.
const (
	ProductTargetChildren ProductTarget = "children"
	ProductTargetMen      ProductTarget = "men"
	ProductTargetUnisex   ProductTarget = "unisex"
	ProductTargetWomen    ProductTarget = "women"
)

// Defines values for ProductTransactionStatus.
const (
	ProductTransactionStatusCanceled             ProductTransactionStatus = "canceled"
	ProductTransactionStatusCompleted            ProductTransactionStatus = "completed"
	ProductTransactionStatusOnHold               ProductTransactionStatus = "onHold"
	ProductTransactionStatusPreparingForShipment ProductTransactionStatus = "preparingForShipment"
	ProductTransactionStatusReceived             ProductTransactionStatus = "received"
	ProductTransactionStatusShipped              ProductTransactionStatus = "shipped"
	ProductTransactionStatusUnderReview          ProductTransactionStatus = "underReview"
	ProductTransactionStatusUnshipped            ProductTransactionStatus = "unshipped"
)

// Defines values for ProductUpdateRequestCondition.
const (
	ProductUpdateRequestConditionLevel1 ProductUpdateRequestCondition = "level1"
	ProductUpdateRequestConditionLevel2 ProductUpdateRequestCondition = "level2"
	ProductUpdateRequestConditionLevel3 ProductUpdateRequestCondition = "level3"
	ProductUpdateRequestConditionLevel4 ProductUpdateRequestCondition = "level4"
	ProductUpdateRequestConditionLevel5 ProductUpdateRequestCondition = "level5"
	ProductUpdateRequestConditionLevel6 ProductUpdateRequestCondition = "level6"
)

// Defines values for ProductUpdateRequestSaleStatus.
const (
	ProductUpdateRequestSaleStatusDraft     ProductUpdateRequestSaleStatus = "draft"
	ProductUpdateRequestSaleStatusOnSale    ProductUpdateRequestSaleStatus = "onSale"
	ProductUpdateRequestSaleStatusSold      ProductUpdateRequestSaleStatus = "sold"
	ProductUpdateRequestSaleStatusSuspended ProductUpdateRequestSaleStatus = "suspended"
)

// Defines values for ProductUpdateRequestTarget.
const (
	ProductUpdateRequestTargetChildren ProductUpdateRequestTarget = "children"
	ProductUpdateRequestTargetMen      ProductUpdateRequestTarget = "men"
	ProductUpdateRequestTargetUnisex   ProductUpdateRequestTarget = "unisex"
	ProductUpdateRequestTargetWomen    ProductUpdateRequestTarget = "women"
)

// Defines values for PurchaseFeeType.
const (
	PurchaseFeeTypePremium PurchaseFeeType = "premium"
	PurchaseFeeTypeRegular PurchaseFeeType = "regular"
)

// Defines values for ReportReporterType.
const (
	ReportReporterTypeSeller ReportReporterType = "seller"
	ReportReporterTypeUser   ReportReporterType = "user"
)

// Defines values for ReportItemType.
const (
	ReportItemTypeProduct ReportItemType = "product"
	ReportItemTypeSeller  ReportItemType = "seller"
	ReportItemTypeUser    ReportItemType = "user"
)

// Defines values for ReportStatus.
const (
	ReportStatusCompleted   ReportStatus = "completed"
	ReportStatusInProgress  ReportStatus = "inProgress"
	ReportStatusNotHandled  ReportStatus = "notHandled"
	ReportStatusPending     ReportStatus = "pending"
	ReportStatusUncompleted ReportStatus = "uncompleted"
)

// Defines values for SellerAccountType.
const (
	Bussiness  SellerAccountType = "bussiness"
	Individual SellerAccountType = "individual"
)

// Defines values for SizeTable.
const (
	General SizeTable = "general"
	Hat     SizeTable = "hat"
	Pants   SizeTable = "pants"
	Ring    SizeTable = "ring"
	Shoes   SizeTable = "shoes"
)

// Defines values for TaxName.
const (
	CONSUMPTIONTAX TaxName = "CONSUMPTION_TAX"
	GST            TaxName = "GST"
	SALESTAX       TaxName = "SALES_TAX"
	VAT            TaxName = "VAT"
)

// Defines values for TermsType.
const (
	BuyerProtection          TermsType = "buyerProtection"
	CommercialTransactionLaw TermsType = "commercialTransactionLaw"
	CommunityGuidelines      TermsType = "communityGuidelines"
	PaymentTerms             TermsType = "paymentTerms"
	PrivacyPolicy            TermsType = "privacyPolicy"
	TermsOfUseForGeneral     TermsType = "termsOfUseForGeneral"
	TermsOfUseForSeller      TermsType = "termsOfUseForSeller"
)

// Defines values for GetReportsParamsSortBy.
const (
	GetReportsParamsSortByCreatedAt GetReportsParamsSortBy = "createdAt"
	GetReportsParamsSortByStatus    GetReportsParamsSortBy = "status"
)

// Defines values for GetSellerProductsParamsSortBy.
const (
	GetSellerProductsParamsSortByCreatedAt GetSellerProductsParamsSortBy = "createdAt"
	GetSellerProductsParamsSortByPrice     GetSellerProductsParamsSortBy = "price"
)

// AccountStatus defines model for AccountStatus.
type AccountStatus string

// AccountType defines model for AccountType.
type AccountType string

// Address defines model for Address.
type Address struct {
	// Address1 Address line 1
	Address1 *string `json:"address1,omitempty"`

	// Address2 Address line 2
	Address2 *string `json:"address2,omitempty"`

	// City City of the address
	City *string `json:"city,omitempty"`

	// Country Country of the address
	Country *string `json:"country,omitempty"`

	// FirstName First name of the user
	FirstName *string `json:"firstName,omitempty"`

	// FullName Full name of the user
	FullName *string `json:"fullName,omitempty"`

	// IsDifferentFromResidence Whether the address is different from the residence address
	IsDifferentFromResidence *bool `json:"isDifferentFromResidence,omitempty"`

	// LastName Last name of the user
	LastName *string `json:"lastName,omitempty"`
	Mobile   *Phone  `json:"mobile,omitempty"`
	Phone    *Phone  `json:"phone,omitempty"`

	// PostalCode Postal code of the address
	PostalCode *string `json:"postalCode,omitempty"`

	// RegionId Region id of the address
	RegionId *string `json:"regionId,omitempty"`

	// State State of the address
	State *string `json:"state,omitempty"`
}

// Admin defines model for Admin.
type Admin struct {
	Id   *string `json:"id,omitempty"`
	Name *string `json:"name,omitempty"`
}

// AuthRequest Auth Request
type AuthRequest struct {
	GrantType    AuthRequestGrantType `json:"grantType" validate:"oneof=password refreshToken"`
	Password     *string              `json:"password,omitempty" validate:"omitempty,gt=0"`
	RefreshToken *string              `json:"refreshToken,omitempty" validate:"omitempty,gt=0"`
	Username     *string              `json:"username,omitempty" validate:"omitempty,gt=0"`
}

// AuthRequestGrantType defines model for AuthRequest.GrantType.
type AuthRequestGrantType string

// AuthResponse Auth Response
type AuthResponse struct {
	// AccessToken Access token
	AccessToken string `json:"accessToken"`

	// RefreshToken Refresh token
	RefreshToken string `json:"refreshToken"`
}

// BanOrderRequest Ban request  object
type BanOrderRequest struct {
	// IsBan default false
	IsBan *bool `json:"isBan,omitempty"`
}

// BankAccount Validation model for bank account profile information
type BankAccount struct {
	// AccountHolderKatakana Account holder name in katakana validation (JP only)
	AccountHolderKatakana *string `json:"accountHolderKatakana,omitempty"`

	// AccountHolderName Account holder name validation
	AccountHolderName string `json:"accountHolderName"`

	// AccountNumber Account number format validation
	AccountNumber string `json:"accountNumber"`

	// AccountType Validated account types
	AccountType BankAccountAccountType `json:"accountType"`

	// BankCode Bank code format validation
	BankCode *string `json:"bankCode,omitempty"`

	// BankName Bank name with length constraints
	BankName string `json:"bankName"`

	// BranchCode Branch code format validation
	BranchCode *string `json:"branchCode,omitempty"`
	BranchName *string `json:"branchName,omitempty"`

	// Country Country/Region
	Country *string `json:"country,omitempty"`

	// Iban IBAN code
	Iban *string `json:"iban,omitempty"`

	// Pin PIN for bank account
	Pin *string `json:"pin,omitempty"`

	// RoutingNumber Routing number
	RoutingNumber *string `json:"routingNumber,omitempty"`

	// SwiftCode SWIFT/BIC code format validation
	SwiftCode *string `json:"swiftCode,omitempty"`
}

// BankAccountAccountType Validated account types
type BankAccountAccountType string

// BankAccountType defines model for BankAccountType.
type BankAccountType string

// Banner defines model for Banner.
type Banner struct {
	Action             *BannerAction                 `json:"action,omitempty"`
	BannerTranslations *map[string]BannerTranslation `json:"bannerTranslations,omitempty"`
	CountryCodes       *[]string                     `json:"countryCodes,omitempty"`
	CreatedAt          *string                       `json:"createdAt,omitempty"`
	CreatedBy          *Admin                        `json:"createdBy,omitempty"`
	Id                 *string                       `json:"id,omitempty"`
	PublishDate        *string                       `json:"publishDate,omitempty"`
	Status             *BannerStatus                 `json:"status,omitempty"`
	UpdatedAt          *string                       `json:"updatedAt,omitempty"`
	UpdatedBy          *Admin                        `json:"updatedBy,omitempty"`
}

// BannerAction defines model for BannerAction.
type BannerAction struct {
	// InAppUrl In-app URL to redirect to
	InAppUrl *string      `json:"inAppUrl,omitempty"`
	Query    *BannerQuery `json:"query,omitempty"`

	// Shop Shop ID to redirect to
	Shop *string `json:"shop,omitempty"`

	// Type Type of banner action
	Type *BannerActionType `json:"type,omitempty"`

	// Url URL to redirect to
	Url *string `json:"url,omitempty"`
}

// BannerActionType Type of banner action
type BannerActionType string

// BannerQuery defines model for BannerQuery.
type BannerQuery struct {
	BrandNames   *[]string `json:"brandNames,omitempty"`
	CategoryIds  *[]string `json:"categoryIds,omitempty"`
	Conditions   *[]string `json:"conditions,omitempty"`
	CountryCodes *[]string `json:"countryCodes,omitempty"`

	// Keyword Keyword to search for
	Keyword *string `json:"keyword,omitempty"`

	// MaxPrice Maximum price to search for
	MaxPrice *float64 `json:"maxPrice,omitempty"`

	// MinPrice Minimum price to search for
	MinPrice     *float64              `json:"minPrice,omitempty"`
	SaleStatuses *[]ProductSalesStatus `json:"saleStatuses,omitempty"`
	Sizes        *[]string             `json:"sizes,omitempty"`
	Targets      *[]ProductTarget      `json:"targets,omitempty"`
}

// BannerStatus defines model for BannerStatus.
type BannerStatus string

// BannerTranslation additionalProperty is language code
type BannerTranslation struct {
	BannerImageUrl *string       `json:"bannerImageUrl,omitempty"`
	HeroImageUrl   *string       `json:"heroImageUrl,omitempty"`
	Position       *[]string     `json:"position,omitempty"`
	Status         *BannerStatus `json:"status,omitempty"`
	Title          *string       `json:"title,omitempty"`
}

// Brand defines model for Brand.
type Brand struct {
	Active *bool `json:"active,omitempty"`

	// Count Number of products for this brand
	Count       *int               `json:"count,omitempty"`
	DisplayName *map[string]string `json:"displayName,omitempty"`
	Id          *string            `json:"id,omitempty" validate:"omitempty,uuid"`
}

// BrandsBranch defines model for BrandsBranch.
type BrandsBranch struct {
	Brands      *[]Brand      `json:"brands,omitempty"`
	CreatedAt   *string       `json:"createdAt,omitempty"`
	Id          *string       `json:"id,omitempty"`
	PublishedAt *string       `json:"publishedAt,omitempty"`
	Status      *MasterStatus `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	UpdatedAt   *string       `json:"updatedAt,omitempty"`
}

// CitesCommonField defines model for CitesCommonField.
type CitesCommonField struct {
	// CitesExportPermit List of CITES export permit documents
	CitesExportPermit *[]string `json:"citesExportPermit,omitempty"`

	// DomesticTradingCertificates Other certificates or transaction documents required for domestic trading in the listing country
	DomesticTradingCertificates *[]string `json:"domesticTradingCertificates,omitempty"`

	// ProductDetailsForReview Product details required for review
	ProductDetailsForReview *string `json:"productDetailsForReview,omitempty"`

	// ProofOfLegalAcquisition List of documents proving legal acquisition
	ProofOfLegalAcquisition *[]string `json:"proofOfLegalAcquisition,omitempty"`
}

// CommissionSale defines model for CommissionSale.
type CommissionSale struct {
	Fee   *float64 `json:"fee,omitempty"`
	Max   *float64 `json:"max,omitempty"`
	Min   *float64 `json:"min,omitempty"`
	Range *float32 `json:"range,omitempty"`
}

// CommissionSalesBranch defines model for CommissionSalesBranch.
type CommissionSalesBranch struct {
	CommissionSales *[]CommissionSale `json:"commissionSales,omitempty"`
	CreatedAt       *string           `json:"createdAt,omitempty"`
	Id              *string           `json:"id,omitempty"`
	PublishedAt     *string           `json:"publishedAt,omitempty"`
	Status          *MasterStatus     `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	UpdatedAt       *string           `json:"updatedAt,omitempty"`
}

// CompanyInfo Validation model for indentity information
type CompanyInfo struct {
	// CompanyName Company name
	CompanyName *string `json:"companyName,omitempty"`

	// ContactName Contact person name
	ContactName *string `json:"contactName,omitempty"`

	// DunsNumber DUNS number
	DunsNumber *string `json:"dunsNumber,omitempty"`

	// Email Company/Contact person's email address
	Email *string `json:"email,omitempty"`

	// PhoneNumber Phone number with international format validation
	PhoneNumber *string `json:"phoneNumber,omitempty"`

	// WebsiteURL Website URL
	WebsiteURL *string `json:"websiteURL,omitempty"`
}

// Content defines model for Content.
type Content struct {
	Content             *map[string]ContentContent `json:"content,omitempty"`
	ContentCategoryCode *string                    `json:"contentCategoryCode,omitempty"`
	CountryCode         *string                    `json:"countryCode,omitempty"`
	CreatedAt           *string                    `json:"createdAt,omitempty"`
	CreatedBy           *Admin                     `json:"createdBy,omitempty"`
	Description         *string                    `json:"description,omitempty"`
	Id                  *string                    `json:"id,omitempty"`
	ParentContentId     *string                    `json:"parentContentId,omitempty"`
	PublishedAt         *string                    `json:"publishedAt,omitempty"`
	Status              *ContentStatus             `json:"status,omitempty" validate:"omitempty,oneof=draft published"`
	Title               *map[string]string         `json:"title,omitempty"`
	UpdatedAt           *string                    `json:"updatedAt,omitempty"`
	UpdatedBy           *Admin                     `json:"updatedBy,omitempty"`
}

// ContentCategory defines model for ContentCategory.
type ContentCategory struct {
	Code        *string            `json:"code,omitempty"`
	ContentType *ContentType       `json:"contentType,omitempty"`
	DisplayName *map[string]string `json:"displayName,omitempty"`
}

// ContentContent defines model for ContentContent.
type ContentContent struct {
	Active      *bool     `json:"active,omitempty"`
	Attachments *[]string `json:"attachments,omitempty"`
	Body        *string   `json:"body,omitempty"`
	Title       *string   `json:"title,omitempty"`
	Url         *string   `json:"url,omitempty"`
}

// ContentField defines model for ContentField.
type ContentField struct {
	Code        *string            `json:"code,omitempty"`
	DisplayName *map[string]string `json:"displayName,omitempty"`
	Required    *bool              `json:"required,omitempty"`
	Type        *string            `json:"type,omitempty"`
}

// ContentStatus defines model for ContentStatus.
type ContentStatus string

// ContentType defines model for ContentType.
type ContentType struct {
	Code   *string         `json:"code,omitempty"`
	Fields *[]ContentField `json:"fields,omitempty"`
}

// Country defines model for Country.
type Country struct {
	Active          *bool            `json:"active,omitempty"`
	BankAccountType *BankAccountType `json:"bankAccountType,omitempty"`

	// Code Country branch code
	Code       *string     `json:"code,omitempty"`
	Currencies *[]Currency `json:"currencies,omitempty"`

	// DisplayName Country branch display name
	DisplayName *map[string]string `json:"displayName,omitempty"`

	// IsOnlyForCorporateMember Country branch is only for corporate member
	IsOnlyForCorporateMember *bool            `json:"isOnlyForCorporateMember,omitempty"`
	Regions                  *[]RegionTax     `json:"regions,omitempty"`
	SaletaxDatabase          *SaletaxDatabase `json:"saletaxDatabase,omitempty"`
	TaxConfig                *TaxConfig       `json:"taxConfig,omitempty"`
	TaxName                  *TaxName         `json:"taxName,omitempty"`
}

// CountryBranch defines model for CountryBranch.
type CountryBranch struct {
	Country     *Country      `json:"country,omitempty"`
	CreatedAt   *string       `json:"createdAt,omitempty"`
	Id          *string       `json:"id,omitempty"`
	PublishedAt *string       `json:"publishedAt,omitempty"`
	Status      *MasterStatus `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	UpdatedAt   *string       `json:"updatedAt,omitempty"`
}

// CountryBreakdown Country breakdown statistics
type CountryBreakdown struct {
	// CountryCode ISO3 country code
	CountryCode string `json:"countryCode"`

	// DisplayName Country display name
	DisplayName *map[string]string `json:"displayName,omitempty"`

	// MarketplaceVAT Marketplace VAT obligations data
	MarketplaceVAT MarketplaceVAT `json:"marketplaceVAT"`

	// PlatformFees Platform fees data
	PlatformFees PlatformFees `json:"platformFees"`

	// SellerRevenue Seller revenue analytics data
	SellerRevenue SellerRevenue `json:"sellerRevenue"`
}

// CountryStatistics User statistics for a specific country
type CountryStatistics struct {
	// ActiveUsers Number of active users in this country
	ActiveUsers int `json:"activeUsers"`

	// Country ISO3 country code
	Country string `json:"country"`

	// DisplayName Country display name
	DisplayName *map[string]string `json:"displayName,omitempty"`

	// GeneralUsers Number of general users in this country
	GeneralUsers int `json:"generalUsers"`

	// PremiumUsers Number of premium users in this country
	PremiumUsers int `json:"premiumUsers"`

	// SellerUsers Number of seller users in this country
	SellerUsers int `json:"sellerUsers"`

	// TotalUsers Total number of users in this country
	TotalUsers int `json:"totalUsers"`
}

// CountryUserSummaryResponse Country user summary response
type CountryUserSummaryResponse struct {
	// Summary User statistics summary containing aggregated data
	Summary UserStatistics `json:"summary"`
}

// CreateUserNotificationRequest User request create notification object
type CreateUserNotificationRequest struct {
	// Content Content of the notification with language support
	Content *map[string]NotificationContent `json:"content,omitempty"`
}

// CreditCard defines model for CreditCard.
type CreditCard struct {
	Brand          *string `json:"brand,omitempty"`
	CardHolderName *string `json:"cardHolderName,omitempty"`
	CardNumber     *string `json:"cardNumber,omitempty"`
	ExpiryMonth    *string `json:"expiryMonth,omitempty"`
	ExpiryYear     *string `json:"expiryYear,omitempty"`
}

// Currency defines model for Currency.
type Currency struct {
	// Code Currency code
	Code *CurrencyCode `json:"code,omitempty"`

	// IsBankAccountCurrency Currency is bank account currency
	IsBankAccountCurrency *bool `json:"isBankAccountCurrency,omitempty"`
}

// CurrencyCode Currency code
type CurrencyCode string

// CustomNGWord defines model for CustomNGWord.
type CustomNGWord struct {
	Active *bool   `json:"active,omitempty"`
	Word   *string `json:"word,omitempty"`
}

// CustomNGWordsBranch defines model for CustomNGWordsBranch.
type CustomNGWordsBranch struct {
	CreatedAt     *string         `json:"createdAt,omitempty"`
	CustomNGWords *[]CustomNGWord `json:"customNGWords,omitempty"`
	Id            *string         `json:"id,omitempty"`
	PublishedAt   *string         `json:"publishedAt,omitempty"`
	Status        *MasterStatus   `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	UpdatedAt     *string         `json:"updatedAt,omitempty"`
}

// DistrictTax defines model for DistrictTax.
type DistrictTax struct {
	// SpecialRate Special rate
	SpecialRate *float64 `json:"specialRate,omitempty"`

	// TaxRegionName District
	TaxRegionName *string `json:"taxRegionName,omitempty"`

	// ZipCode Zip code
	ZipCode *string `json:"zipCode,omitempty"`
}

// Email defines model for Email.
type Email struct {
	Email *string `json:"email,omitempty"`
}

// ErrorResponse Response when abnormal
type ErrorResponse struct {
	// Code Error code
	Code *string `json:"code,omitempty"`

	// DebugMessage Debug message (for development)
	DebugMessage *string `json:"debug_message,omitempty"`

	// Message Error message
	Message *string `json:"message,omitempty"`

	// RequestId Request id where the error occurred
	RequestId *string `json:"request_id,omitempty"`
}

// FaqCategoriesBranch defines model for FaqCategoriesBranch.
type FaqCategoriesBranch struct {
	CreatedAt     *string        `json:"createdAt,omitempty"`
	FaqCategories *[]FaqCategory `json:"faqCategories,omitempty"`
	Id            *string        `json:"id,omitempty"`
	PublishedAt   *string        `json:"publishedAt,omitempty"`
	Status        *ContentStatus `json:"status,omitempty" validate:"omitempty,oneof=draft published"`
	UpdatedAt     *string        `json:"updatedAt,omitempty"`
}

// FaqCategory defines model for FaqCategory.
type FaqCategory struct {
	Active        *bool              `json:"active,omitempty"`
	Count         *int               `json:"count,omitempty"`
	DisplayName   *map[string]string `json:"displayName,omitempty"`
	Id            *string            `json:"id,omitempty" validate:"omitempty,uuid"`
	SubCategories *[]FaqCategory     `json:"subCategories,omitempty"`
}

// GetCountriesResponse defines model for GetCountriesResponse.
type GetCountriesResponse struct {
	Items *[]Country `json:"items,omitempty"`
}

// GetInquiryStatsAreaServiceResponse Get inquiry stats area service response object
type GetInquiryStatsAreaServiceResponse struct {
	Data       *[]InquiryLocationStats `json:"data,omitempty"`
	Pagination *Pagination             `json:"pagination,omitempty"`
}

// GetListEmailResponse Get List Emails
type GetListEmailResponse struct {
	Data *[]Email `json:"data,omitempty"`
}

// GetNotificationsResponse Get notifications response object
type GetNotificationsResponse struct {
	Data       *[]Notification `json:"data,omitempty"`
	Pagination *Pagination     `json:"pagination,omitempty"`
}

// GetOrderDetailsResponse Get Order Details response object
type GetOrderDetailsResponse struct {
	Data       *[]PurchasedItem `json:"data,omitempty"`
	Pagination *Pagination      `json:"pagination,omitempty"`
}

// GetOrderItemResponse Get Order Item response object
type GetOrderItemResponse struct {
	Data *[]PurchasedItem `json:"data,omitempty"`
}

// GetOrderResponse Get Order Item response object
type GetOrderResponse struct {
	Data       *[]Order    `json:"data,omitempty"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

// GetReportsResponse Get reports response object
type GetReportsResponse struct {
	Data       *[]Report   `json:"data,omitempty"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

// GetSellerProfileResponse Seller profile information for admin
type GetSellerProfileResponse struct {
	Address *Address `json:"address,omitempty"`

	// BankAccount Validation model for bank account profile information
	BankAccount *BankAccount `json:"bankAccount,omitempty"`

	// CompanyInfo Validation model for indentity information
	CompanyInfo *CompanyInfo `json:"companyInfo,omitempty"`

	// Kyc Validation model for indentity information
	Kyc *Kyc `json:"kyc,omitempty"`

	// Rating Seller rating object
	Rating *SellerRating `json:"rating,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller Seller `json:"seller"`
}

// GetUserByIdResponse User response get object
type GetUserByIdResponse struct {
	AccountId      *string  `json:"accountId,omitempty"`
	BillingAddress *Address `json:"billingAddress,omitempty"`

	// CompanyInfo Validation model for indentity information
	CompanyInfo *CompanyInfo `json:"companyInfo,omitempty"`

	// ConfirmTermDate Date of confirmation of the user. Format: YYYY-MM-DD
	ConfirmTermDate *string `json:"confirmTermDate,omitempty"`

	// CountryCode Country code of the user
	CountryCode *string     `json:"countryCode,omitempty"`
	CreatedAt   *string     `json:"createdAt,omitempty"`
	CreditCard  *CreditCard `json:"creditCard,omitempty"`

	// DateOfBirth Date of birth of the user. Format: YYYY-MM-DD
	DateOfBirth *string `json:"dateOfBirth,omitempty"`
	DeletedAt   *string `json:"deletedAt,omitempty"`

	// Email User's email address
	Email *string `json:"email,omitempty"`

	// FirstName First name of the user
	FirstName *string `json:"firstName,omitempty"`

	// Gender Gender of the user
	Gender      *string  `json:"gender,omitempty"`
	HomeAddress *Address `json:"homeAddress,omitempty"`

	// Id User's unique identifier
	Id *string `json:"id,omitempty"`

	// IsTermsRequired Whether the user has confirmed the terms and conditions
	IsTermsRequired *bool   `json:"isTermsRequired,omitempty"`
	Language        *string `json:"language,omitempty"`

	// LastName Last name of the user
	LastName   *string `json:"lastName,omitempty"`
	Membership *int    `json:"membership,omitempty"`

	// Name Name of the user
	Name *string `json:"name,omitempty"`

	// Nickname Nickname of the user
	Nickname *string `json:"nickname,omitempty"`

	// PhoneNumber User's phone number
	PhoneNumber *string `json:"phoneNumber,omitempty"`

	// PinSetting Whether the user has set a PIN
	PinSetting *bool `json:"pinSetting,omitempty"`

	// ReceiveNewsletter Whether the user wants to receive newsletter
	ReceiveNewsletter *bool `json:"receiveNewsletter,omitempty"`

	// RegionId Region ID of the user
	RegionId *string `json:"regionId,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller *Seller `json:"seller,omitempty"`

	// SellerId Seller ID of the user
	SellerId        *string  `json:"sellerId,omitempty"`
	ShippingAddress *Address `json:"shippingAddress,omitempty"`
	Status          *string  `json:"status,omitempty"`
	UpdatedAt       *string  `json:"updatedAt,omitempty"`
}

// GlobalUserSummaryResponse Global user summary response
type GlobalUserSummaryResponse struct {
	// Summary User statistics summary containing aggregated data
	Summary UserStatistics `json:"summary"`
}

// InquiryLocationStats defines model for InquiryLocationStats.
type InquiryLocationStats struct {
	City         *string `json:"city,omitempty"`
	CityCount    *int    `json:"cityCount,omitempty"`
	Country      *string `json:"country,omitempty"`
	CountryCount *int    `json:"countryCount,omitempty"`
	State        *string `json:"state,omitempty"`
	StateCount   *int    `json:"stateCount,omitempty"`
}

// Kyc Validation model for indentity information
type Kyc struct {
	// KycImgUrl Url to KYC image (S3, CDN, or any...)
	KycImgUrl *string `json:"kycImgUrl,omitempty"`

	// KycType Proof of indentity
	KycType *KycKycType `json:"kycType,omitempty"`
}

// KycKycType Proof of indentity
type KycKycType string

// Language defines model for Language.
type Language struct {
	Active      *bool              `json:"active,omitempty"`
	Code        *string            `json:"code,omitempty"`
	DisplayName *map[string]string `json:"displayName,omitempty"`
}

// LanguagesBranch defines model for LanguagesBranch.
type LanguagesBranch struct {
	CreatedAt   *string       `json:"createdAt,omitempty"`
	Id          *string       `json:"id,omitempty"`
	Languages   *[]Language   `json:"languages,omitempty"`
	PublishedAt *string       `json:"publishedAt,omitempty"`
	Status      *MasterStatus `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	UpdatedAt   *string       `json:"updatedAt,omitempty"`
}

// MaintenanceStatus defines model for MaintenanceStatus.
type MaintenanceStatus struct {
	// EndTime When maintenance mode was ended (null if still in maintenance)
	EndTime *time.Time `json:"endTime"`

	// IsMaintenanceMode Whether the system is currently in maintenance mode
	IsMaintenanceMode bool `json:"isMaintenanceMode"`

	// StartTime When maintenance mode was started (null if not in maintenance)
	StartTime *time.Time `json:"startTime"`
}

// MaintenanceUpdateRequest defines model for MaintenanceUpdateRequest.
type MaintenanceUpdateRequest struct {
	// IsMaintenanceMode Set to true to enable maintenance mode, false to disable
	IsMaintenanceMode bool `json:"isMaintenanceMode"`
}

// MarketplaceVAT Marketplace VAT obligations data
type MarketplaceVAT struct {
	// VatObligations Revenue from orders where MP must pay VAT (orders with tax_amount > 0 but sales_tax_amount = 0), grouped by tax rate
	VatObligations []VatObligation `json:"vatObligations"`
}

// MasterStatus defines model for MasterStatus.
type MasterStatus string

// Notification Notification model representing a single notification
type Notification struct {
	// Content Content of the notification
	Content      *map[string]NotificationArticleContent `json:"content,omitempty"`
	CountryCodes *[]string                              `json:"countryCodes,omitempty"`

	// CreatedAt Timestamp when the notification was created
	CreatedAt *string `json:"createdAt,omitempty"`

	// Id Unique identifier for the notification
	Id *string `json:"id,omitempty"`

	// PublishedAt Timestamp when the notification was published
	PublishedAt *string        `json:"publishedAt,omitempty"`
	Status      *ContentStatus `json:"status,omitempty" validate:"omitempty,oneof=draft published"`

	// Title Title of the notification
	Title *map[string]string `json:"title,omitempty"`

	// Type Type of the notification
	Type *NotificationType `json:"type,omitempty"`

	// UpdatedAt Timestamp when the notification was updated
	UpdatedAt *string `json:"updatedAt,omitempty"`
}

// NotificationArticleContent Content of the notification
type NotificationArticleContent struct {
	// Active Indicates if the notification is active
	Active *bool `json:"active,omitempty"`

	// ArticleImages Article images of the notification
	ArticleImages *[]string `json:"articleImages,omitempty"`

	// KeyImage Key image of the notification
	KeyImage *string `json:"keyImage,omitempty"`

	// Message Message of the notification
	Message *string `json:"message,omitempty"`

	// Title Title of the notification
	Title *string `json:"title,omitempty"`
}

// NotificationContent Content message of the notification
type NotificationContent struct {
	// Message Message of the notification
	Message *string `json:"message,omitempty"`
}

// NotificationSettingsType defines model for NotificationSettingsType.
type NotificationSettingsType string

// NotificationType Type of the notification
type NotificationType string

// OptionalTax defines model for OptionalTax.
type OptionalTax struct {
	// CategoryIds Category IDs
	CategoryIds *[]string `json:"categoryIds,omitempty"`

	// IsOverflow Is overflow
	IsOverflow *bool `json:"isOverflow,omitempty"`

	// MaxAmount Max
	MaxAmount *float64 `json:"maxAmount,omitempty"`

	// MinAmount Min
	MinAmount *float64 `json:"minAmount,omitempty"`

	// TaxRate Tax rate
	TaxRate *float64 `json:"taxRate,omitempty"`
}

// Order defines model for Order.
type Order struct {
	AdministrativeFee          *float64         `json:"administrativeFee,omitempty"`
	AdministrativeFeeRate      *float64         `json:"administrativeFeeRate,omitempty"`
	AdministrativeFeeTaxAmount *float64         `json:"administrativeFeeTaxAmount,omitempty"`
	AdministrativeFeeTaxRate   *float64         `json:"administrativeFeeTaxRate,omitempty"`
	Amount                     *float64         `json:"amount,omitempty"`
	CompletedAt                *string          `json:"completedAt,omitempty"`
	CreatedAt                  *string          `json:"createdAt,omitempty"`
	Id                         *string          `json:"id,omitempty"`
	IsBan                      *bool            `json:"isBan,omitempty"`
	Items                      *[]PurchasedItem `json:"items,omitempty"`
	OrderNumber                *string          `json:"orderNumber,omitempty"`
	PaymentMethod              *CreditCard      `json:"paymentMethod,omitempty"`
	PurchaseFee                *float64         `json:"purchaseFee,omitempty"`
	PurchaseFeeTaxAmount       *float64         `json:"purchaseFeeTaxAmount,omitempty"`
	PurchaseFeeTaxRate         *float64         `json:"purchaseFeeTaxRate,omitempty"`
	ShippingAddress            *Address         `json:"shippingAddress,omitempty"`
	TaxAmount                  *float64         `json:"taxAmount,omitempty"`
	TaxDetails                 *[]TaxDetail     `json:"taxDetails,omitempty"`

	// TaxInfo Tax information for an order
	TaxInfo     *TaxInfo `json:"taxInfo,omitempty"`
	TaxRate     *float64 `json:"taxRate,omitempty"`
	TotalAmount *float64 `json:"totalAmount,omitempty"`
	UpdatedAt   *string  `json:"updatedAt,omitempty"`
	User        *User    `json:"user,omitempty"`
}

// OrderBy defines model for OrderBy.
type OrderBy string

// OrderStatistics Comprehensive order statistics data
type OrderStatistics struct {
	// CountryBreakdown Statistics broken down by country
	CountryBreakdown []CountryBreakdown `json:"countryBreakdown"`

	// MarketplaceVAT Marketplace VAT obligations data
	MarketplaceVAT MarketplaceVAT `json:"marketplaceVAT"`

	// PlatformFees Platform fees data
	PlatformFees PlatformFees `json:"platformFees"`

	// RegionBreakdown Statistics broken down by region
	RegionBreakdown []RegionBreakdown `json:"regionBreakdown"`

	// SellerRevenue Seller revenue analytics data
	SellerRevenue SellerRevenue `json:"sellerRevenue"`
}

// OrderStatisticsResponse Order statistics response object
type OrderStatisticsResponse struct {
	// Data Comprehensive order statistics data
	Data OrderStatistics `json:"data"`
}

// Pagination defines model for Pagination.
type Pagination struct {
	// Limit number of items per page
	Limit int `json:"limit" validate:"required,gte=1"`

	// Page number of selected page
	Page int `json:"page" validate:"required,gte=1"`

	// TotalCount number of record
	TotalCount *int `json:"totalCount,omitempty"`
}

// Phone defines model for Phone.
type Phone struct {
	// CountryCode The country code of the phone number without the plus sign (+) (e.g. in Japan it is 81)
	CountryCode string `json:"countryCode"`
	Number      string `json:"number"`
}

// PlatformFees Platform fees data
type PlatformFees struct {
	// BuyingFees Buying fees including taxes
	BuyingFees float64 `json:"buyingFees"`

	// MemberDiscount Total member discounts applied
	MemberDiscount float64 `json:"memberDiscount"`

	// SellingFees Selling fees including taxes
	SellingFees float64 `json:"sellingFees"`
}

// PostalCodesResponse Response containing list of valid US postal codes
type PostalCodesResponse struct {
	// Count Total number of postal codes returned
	Count int `json:"count"`

	// PostalCodes Array of valid US postal codes (5-digit ZIP codes)
	PostalCodes []string `json:"postalCodes"`
}

// Product defines model for Product.
type Product struct {
	Brand *ProductBrand `json:"brand,omitempty"`

	// BrandId Brand of the product
	BrandId *string `json:"brandId,omitempty"`

	// BrandNameOther Brand other of the product in case of not in the list
	BrandNameOther *string            `json:"brandNameOther,omitempty"`
	Categories     *[]ProductCategory `json:"categories,omitempty"`

	// CategoryId Category of the product
	CategoryId       *string           `json:"categoryId,omitempty"`
	CitesInformation *CitesCommonField `json:"citesInformation,omitempty"`
	Condition        *ProductCondition `json:"condition,omitempty"`
	CreatedAt        *time.Time        `json:"createdAt,omitempty"`

	// Description Detailed description of the product
	Description *map[string]string `json:"description,omitempty"`

	// DiscountAmount Discount amount applied to the product
	DiscountAmount *float64 `json:"discountAmount,omitempty"`

	// DiscountRate Discount percentage applied to the product
	DiscountRate *float64 `json:"discountRate,omitempty"`

	// Id The unique identifier for the product
	Id *string `json:"id,omitempty"`

	// Images List of product images
	Images *[]string `json:"images,omitempty"`

	// IsCITESRestricted Whether the product is subject to CITES restrictions
	IsCITESRestricted *bool `json:"isCITESRestricted,omitempty"`

	// IsDiscount Does the product have a discount?
	IsDiscount *bool `json:"isDiscount,omitempty"`

	// IsFavorite Whether the product is in the user's favorites
	IsFavorite *bool `json:"isFavorite,omitempty"`

	// Name Name of the product
	Name      *map[string]string `json:"name,omitempty"`
	OrderItem *PurchasedItem     `json:"orderItem,omitempty"`

	// Price Price of the product
	Price *float64 `json:"price,omitempty"`

	// ReleaseDate Timestamp of when the product was released
	ReleaseDate *string             `json:"releaseDate,omitempty"`
	SalesStatus *ProductSalesStatus `json:"salesStatus,omitempty"`

	// ShipFrom Country of the product
	ShipFrom *string `json:"shipFrom,omitempty"`

	// ShippingMethod Shipping method of the product
	ShippingMethod *string `json:"shippingMethod,omitempty"`

	// Size Size information (if applicable)
	Size              *string                   `json:"size,omitempty"`
	SizeDetails       *string                   `json:"sizeDetails,omitempty"`
	Target            *ProductTarget            `json:"target,omitempty"`
	TransactionStatus *ProductTransactionStatus `json:"transactionStatus,omitempty"`
}

// ProductBanRequest Product request ban object
type ProductBanRequest struct {
	IsBanned *bool `json:"isBanned,omitempty"`
}

// ProductBrand defines model for ProductBrand.
type ProductBrand struct {
	DisplayName *map[string]string `json:"displayName,omitempty"`
	Id          *string            `json:"id,omitempty"`
}

// ProductCategoriesBranch defines model for ProductCategoriesBranch.
type ProductCategoriesBranch struct {
	CreatedAt         *string            `json:"createdAt,omitempty"`
	Id                *string            `json:"id,omitempty"`
	ProductCategories *[]ProductCategory `json:"productCategories,omitempty"`
	PublishedAt       *string            `json:"publishedAt,omitempty"`
	Status            *MasterStatus      `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	UpdatedAt         *string            `json:"updatedAt,omitempty"`
}

// ProductCategory defines model for ProductCategory.
type ProductCategory struct {
	Active *bool `json:"active,omitempty"`

	// Count Number of products in this category
	Count         *int               `json:"count,omitempty"`
	DisplayName   *map[string]string `json:"displayName,omitempty"`
	Id            *string            `json:"id,omitempty" validate:"omitempty,uuid"`
	SizeTable     *SizeTable         `json:"sizeTable,omitempty"`
	SubCategories *[]ProductCategory `json:"subCategories,omitempty"`
}

// ProductCondition defines model for ProductCondition.
type ProductCondition string

// ProductSalesStatus defines model for ProductSalesStatus.
type ProductSalesStatus string

// ProductTarget defines model for ProductTarget.
type ProductTarget string

// ProductTransactionStatus defines model for ProductTransactionStatus.
type ProductTransactionStatus string

// ProductUpdateRequest Product request update object
type ProductUpdateRequest struct {
	// BrandId Brand of the product
	BrandId *string `json:"brandId,omitempty"`

	// CategoryId Category of the product
	CategoryId *string `json:"categoryId,omitempty"`

	// Condition Condition of the product
	Condition *ProductUpdateRequestCondition `json:"condition,omitempty"`

	// Description Description of the product
	Description *map[string]string `json:"description,omitempty"`

	// DiscountAmount Discount amount applied to the product
	DiscountAmount *float64 `json:"discountAmount,omitempty"`

	// DiscountRate Discount percentage applied to the product
	DiscountRate *float64 `json:"discountRate,omitempty"`

	// Images List of product images
	Images *[]string `json:"images,omitempty"`

	// IsDiscount Does the product have a discount?
	IsDiscount *bool `json:"isDiscount,omitempty"`

	// Name Name of the product
	Name *map[string]string `json:"name,omitempty"`

	// Price Price of the product
	Price *float64 `json:"price,omitempty"`

	// ReleaseDate Timestamp of when the product was released
	ReleaseDate *time.Time `json:"releaseDate,omitempty"`

	// SaleStatus Status of the product
	SaleStatus *ProductUpdateRequestSaleStatus `json:"saleStatus,omitempty"`

	// Size Size information (if applicable)
	Size *string `json:"size,omitempty"`

	// Target Target
	Target *ProductUpdateRequestTarget `json:"target,omitempty"`
}

// ProductUpdateRequestCondition Condition of the product
type ProductUpdateRequestCondition string

// ProductUpdateRequestSaleStatus Status of the product
type ProductUpdateRequestSaleStatus string

// ProductUpdateRequestTarget Target
type ProductUpdateRequestTarget string

// PublishBrandsBranchRequest defines model for PublishBrandsBranchRequest.
type PublishBrandsBranchRequest struct {
	// PublishedAt The date and time the brands branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishCommissionSalesBranchRequest defines model for PublishCommissionSalesBranchRequest.
type PublishCommissionSalesBranchRequest struct {
	// PublishedAt The date and time the commission sales branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishCountryBranchRequest defines model for PublishCountryBranchRequest.
type PublishCountryBranchRequest struct {
	// PublishedAt The date and time the country branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishCustomNGWordsBranchRequest defines model for PublishCustomNGWordsBranchRequest.
type PublishCustomNGWordsBranchRequest struct {
	// PublishedAt The date and time the custom ng words branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishLanguagesBranchRequest defines model for PublishLanguagesBranchRequest.
type PublishLanguagesBranchRequest struct {
	// PublishedAt The date and time the languages branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishProductCategoriesBranchRequest defines model for PublishProductCategoriesBranchRequest.
type PublishProductCategoriesBranchRequest struct {
	// PublishedAt The date and time the product categories branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishPurchaseFeesBranchRequest defines model for PublishPurchaseFeesBranchRequest.
type PublishPurchaseFeesBranchRequest struct {
	// PublishedAt The date and time the purchase fees branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishSubscriptionFeesBranchRequest defines model for PublishSubscriptionFeesBranchRequest.
type PublishSubscriptionFeesBranchRequest struct {
	// PublishedAt The date and time the subscription fees branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishTermsBranchRequest defines model for PublishTermsBranchRequest.
type PublishTermsBranchRequest struct {
	// PublishedAt The date and time the terms branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishTransferFeesBranchRequest defines model for PublishTransferFeesBranchRequest.
type PublishTransferFeesBranchRequest struct {
	// PublishedAt The date and time the transfer fees branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PublishUiWordsBranchRequest defines model for PublishUiWordsBranchRequest.
type PublishUiWordsBranchRequest struct {
	// PublishedAt The date and time the ui words branch was published (YYYY-MM-DD)
	PublishedAt string `json:"publishedAt" validate:"required,dateOnly"`
}

// PurchaseFee defines model for PurchaseFee.
type PurchaseFee struct {
	AdminFeeRate *float64         `json:"adminFeeRate,omitempty"`
	Max          *float64         `json:"max,omitempty"`
	Min          *float64         `json:"min,omitempty"`
	PurchaseFee  *float64         `json:"purchaseFee,omitempty"`
	Range        *int             `json:"range,omitempty"`
	Type         *PurchaseFeeType `json:"type,omitempty"`
}

// PurchaseFeeType defines model for PurchaseFeeType.
type PurchaseFeeType string

// PurchaseFeesBranch defines model for PurchaseFeesBranch.
type PurchaseFeesBranch struct {
	CreatedAt    *string        `json:"createdAt,omitempty"`
	Id           *string        `json:"id,omitempty"`
	PublishedAt  *string        `json:"publishedAt,omitempty"`
	PurchaseFees *[]PurchaseFee `json:"purchaseFees,omitempty"`
	Status       *MasterStatus  `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	UpdatedAt    *string        `json:"updatedAt,omitempty"`
}

// PurchasedItem defines model for PurchasedItem.
type PurchasedItem struct {
	Amount         *float64   `json:"amount,omitempty"`
	CompletedDate  *time.Time `json:"completedDate,omitempty"`
	CreatedAt      *time.Time `json:"createdAt,omitempty"`
	DiscountAmount *float64   `json:"discountAmount,omitempty"`
	Id             *string    `json:"id,omitempty"`
	Note           *string    `json:"note,omitempty"`
	Order          *Order     `json:"order,omitempty"`
	OrderEnd       *string    `json:"orderEnd,omitempty"`
	OrderId        *string    `json:"orderId,omitempty"`
	OrderNumber    *string    `json:"orderNumber,omitempty"`
	Product        *Product   `json:"product,omitempty"`
	ProductId      *string    `json:"productId,omitempty"`
	Rating         *Rating    `json:"rating,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller            *Seller                   `json:"seller,omitempty"`
	ShippedDate       *time.Time                `json:"shippedDate,omitempty"`
	TaxAmount         *float64                  `json:"taxAmount,omitempty"`
	TaxInfos          *[]TaxItemInfo            `json:"taxInfos,omitempty"`
	TaxRate           *float64                  `json:"taxRate,omitempty"`
	TotalAmount       *float64                  `json:"totalAmount,omitempty"`
	TransactionStatus *ProductTransactionStatus `json:"transactionStatus,omitempty"`
	WaybillNumber     *string                   `json:"waybillNumber,omitempty"`
}

// Rating defines model for Rating.
type Rating struct {
	PackRating   *float64 `json:"packRating,omitempty"`
	PoliteRating *float64 `json:"politeRating,omitempty"`
	Rating       *float64 `json:"rating,omitempty"`
	SpeedRating  *float64 `json:"speedRating,omitempty"`
}

// RegionBreakdown Region breakdown statistics
type RegionBreakdown struct {
	// DisplayName Region display name
	DisplayName *map[string]string `json:"displayName,omitempty"`

	// MarketplaceVAT Marketplace VAT obligations data
	MarketplaceVAT MarketplaceVAT `json:"marketplaceVAT"`

	// PlatformFees Platform fees data
	PlatformFees PlatformFees `json:"platformFees"`

	// RegionId Region ID
	RegionId string `json:"regionId"`

	// SellerRevenue Seller revenue analytics data
	SellerRevenue SellerRevenue `json:"sellerRevenue"`
}

// RegionStatistics User statistics for a specific region
type RegionStatistics struct {
	// ActiveUsers Number of active users in this region
	ActiveUsers int `json:"activeUsers"`

	// DisplayName Region display name
	DisplayName *map[string]string `json:"displayName,omitempty"`

	// GeneralUsers Number of general users in this region
	GeneralUsers int `json:"generalUsers"`

	// PremiumUsers Number of premium users in this region
	PremiumUsers int `json:"premiumUsers"`

	// RegionId Unique identifier for the region
	RegionId string `json:"regionId"`

	// SellerUsers Number of seller users in this region
	SellerUsers int `json:"sellerUsers"`

	// TotalUsers Total number of users in this region
	TotalUsers int `json:"totalUsers"`
}

// RegionTax defines model for RegionTax.
type RegionTax struct {
	Active *bool `json:"active,omitempty"`

	// Code Region code
	Code *string `json:"code,omitempty"`

	// DisplayName Region display name
	DisplayName *map[string]string `json:"displayName,omitempty"`
	Id          *string            `json:"id,omitempty"`
	Regions     *[]RegionTax       `json:"regions,omitempty"`
	TaxConfig   *TaxConfig         `json:"taxConfig,omitempty"`
}

// RegionUserSummaryResponse Region user summary response
type RegionUserSummaryResponse struct {
	// Summary User statistics summary containing aggregated data
	Summary UserStatistics `json:"summary"`
}

// Report defines model for Report.
type Report struct {
	CreatedAt    *string             `json:"createdAt,omitempty"`
	Detail       *string             `json:"detail,omitempty"`
	Id           *string             `json:"id,omitempty"`
	Item         *Report_Item        `json:"item,omitempty"`
	ItemId       *string             `json:"itemId,omitempty"`
	ItemType     *ReportItemType     `json:"itemType,omitempty"`
	Reporter     *Report_Reporter    `json:"reporter,omitempty"`
	ReporterId   *string             `json:"reporterId,omitempty"`
	ReporterType *ReportReporterType `json:"reporterType,omitempty"`
	Status       *ReportStatus       `json:"status,omitempty"`
	UpdatedAt    *string             `json:"updatedAt,omitempty"`
}

// Report_Item defines model for Report.Item.
type Report_Item struct {
	union json.RawMessage
}

// Report_Reporter defines model for Report.Reporter.
type Report_Reporter struct {
	union json.RawMessage
}

// ReportReporterType defines model for Report.ReporterType.
type ReportReporterType string

// ReportItemType defines model for ReportItemType.
type ReportItemType string

// ReportStatus defines model for ReportStatus.
type ReportStatus string

// SaletaxDatabase defines model for SaletaxDatabase.
type SaletaxDatabase struct {
	// CitiesCount Cities count
	CitiesCount *int `json:"citiesCount,omitempty"`

	// CountiesCount Counties count
	CountiesCount *int           `json:"countiesCount,omitempty"`
	DistrictTaxes *[]DistrictTax `json:"districtTaxes,omitempty"`

	// RecordsCount Records count
	RecordsCount *int `json:"recordsCount,omitempty"`

	// StatesCount States count
	StatesCount *int `json:"statesCount,omitempty"`
}

// SearchBannersResponse Search banners response object
type SearchBannersResponse struct {
	Data       *[]Banner   `json:"data,omitempty"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

// SearchProductsRequest Search products request object
type SearchProductsRequest struct {
	Action     *BannerAction `json:"action,omitempty"`
	Pagination *Pagination   `json:"pagination,omitempty"`
}

// SearchProductsResponse Search banner products response object
type SearchProductsResponse struct {
	Data       *[]Product  `json:"data,omitempty"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

// SearchUsersResponse Search users response object
type SearchUsersResponse struct {
	Data       *[]User     `json:"data,omitempty"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
type Seller struct {
	// About Something about shop
	About *map[string]string `json:"about,omitempty"`

	// AccountId Account ID
	AccountId *string `json:"accountId,omitempty"`

	// AccountType Validated account types
	AccountType SellerAccountType `json:"accountType"`

	// AvatarUrl Url to avatar (S3, CDN, or any...)
	AvatarUrl *string `json:"avatarUrl,omitempty"`

	// FavoriteBrands List of favorite brands
	FavoriteBrands *[]string `json:"favoriteBrands,omitempty"`

	// HeaderImgUrl Url to header image (S3, CDN, or any...)
	HeaderImgUrl *string `json:"headerImgUrl,omitempty"`

	// Id Seller ID
	Id *string `json:"id,omitempty"`

	// IsFavorite Whether the seller is a favorite of the user
	IsFavorite *bool `json:"isFavorite,omitempty"`

	// PaypalId PayPal payer ID from billing agreement token
	PaypalId *string `json:"paypalId,omitempty"`

	// ShopName Shop name
	ShopName string `json:"shopName"`

	// Specialty Specialty
	Specialty *map[string]string `json:"specialty,omitempty"`

	// StockLocation Stock locaiton. (Ship from)
	StockLocation *string `json:"stockLocation,omitempty"`
}

// SellerAccountType Validated account types
type SellerAccountType string

// SellerProductsResponse Get products response object
type SellerProductsResponse struct {
	Data       *[]Product  `json:"data,omitempty"`
	Pagination *Pagination `json:"pagination,omitempty"`
}

// SellerRating Seller rating object
type SellerRating struct {
	AvgDaysToShip   *float64 `json:"avgDaysToShip,omitempty"`
	AvgPackRating   *float64 `json:"avgPackRating,omitempty"`
	AvgPoliteRating *float64 `json:"avgPoliteRating,omitempty"`
	AvgRating       *float64 `json:"avgRating,omitempty"`
	AvgSpeedRating  *float64 `json:"avgSpeedRating,omitempty"`
	TotalOrders     *int     `json:"totalOrders,omitempty"`
}

// SellerRevenue Seller revenue analytics data
type SellerRevenue struct {
	// OrderCount Total number of orders
	OrderCount int `json:"orderCount"`

	// SellerTaxes Taxes that seller must pay, grouped by tax rate
	SellerTaxes []SellerTax `json:"sellerTaxes"`

	// TotalOrderAmount Total price of all orders from seller
	TotalOrderAmount float64 `json:"totalOrderAmount"`

	// TotalProductPrice Total price of products sold by seller
	TotalProductPrice float64 `json:"totalProductPrice"`

	// TotalSalesAmount Confirmed revenue including taxes for seller
	TotalSalesAmount float64 `json:"totalSalesAmount"`
}

// SellerTax Seller tax information grouped by tax rate
type SellerTax struct {
	// TaxAmount Tax amount for this rate
	TaxAmount float64 `json:"taxAmount"`

	// TaxRate Tax rate percentage (e.g., 8 for 8%)
	TaxRate float64 `json:"taxRate"`
}

// SizeTable defines model for SizeTable.
type SizeTable string

// SubscriptionFee defines model for SubscriptionFee.
type SubscriptionFee struct {
	Currency     *string            `json:"currency,omitempty"`
	DisplayName  *map[string]string `json:"displayName,omitempty"`
	LanguageCode *string            `json:"languageCode,omitempty"`
	Price        *float64           `json:"price,omitempty"`
	Type         *string            `json:"type,omitempty"`
}

// SubscriptionFeesBranch defines model for SubscriptionFeesBranch.
type SubscriptionFeesBranch struct {
	CreatedAt        *string            `json:"createdAt,omitempty"`
	Id               *string            `json:"id,omitempty"`
	PublishedAt      *string            `json:"publishedAt,omitempty"`
	Status           *MasterStatus      `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	SubscriptionFees *[]SubscriptionFee `json:"subscriptionFees,omitempty"`
	UpdatedAt        *string            `json:"updatedAt,omitempty"`
}

// TaxConfig defines model for TaxConfig.
type TaxConfig struct {
	// IsAdministrativeFeeTax Is administrative fee tax
	IsAdministrativeFeeTax *bool `json:"isAdministrativeFeeTax,omitempty"`

	// IsMarketplaceOperators Is marketplace operators
	IsMarketplaceOperators *bool `json:"isMarketplaceOperators,omitempty"`

	// IsPurchaseFeeTax Is purchase fee tax
	IsPurchaseFeeTax *bool `json:"isPurchaseFeeTax,omitempty"`

	// IsRemittanceFeeTax Is sales commission fee tax
	IsRemittanceFeeTax *bool `json:"isRemittanceFeeTax,omitempty"`

	// IsSalesCommissionFeeTax Is sales commission fee tax
	IsSalesCommissionFeeTax *bool          `json:"isSalesCommissionFeeTax,omitempty"`
	OptionalTax             *[]OptionalTax `json:"optionalTax,omitempty"`

	// TaxRate Tax rate
	TaxRate *float64 `json:"taxRate,omitempty"`
}

// TaxDetail defines model for TaxDetail.
type TaxDetail struct {
	Amount    *float64 `json:"amount,omitempty"`
	TaxAmount *float64 `json:"taxAmount,omitempty"`
	TaxRate   *float64 `json:"taxRate,omitempty"`
}

// TaxInfo Tax information for an order
type TaxInfo struct {
	Country    *Country     `json:"country,omitempty"`
	Regions    *[]RegionTax `json:"regions,omitempty"`
	TaxDetails *[]TaxDetail `json:"taxDetails,omitempty"`
	TaxName    *TaxName     `json:"taxName,omitempty"`
}

// TaxItemInfo Tax information for an order
type TaxItemInfo struct {
	Country    *Country     `json:"country,omitempty"`
	District   *RegionTax   `json:"district,omitempty"`
	Region     *RegionTax   `json:"region,omitempty"`
	TaxDetails *[]TaxDetail `json:"taxDetails,omitempty"`
	TaxName    *TaxName     `json:"taxName,omitempty"`
}

// TaxName defines model for TaxName.
type TaxName string

// Terms defines model for Terms.
type Terms struct {
	Active      *bool                      `json:"active,omitempty"`
	Content     *map[string]ContentContent `json:"content,omitempty"`
	CountryCode *string                    `json:"countryCode,omitempty"`
	IsRequired  *bool                      `json:"isRequired,omitempty"`
	VersionId   *int                       `json:"versionId,omitempty"`
}

// TermsBranch defines model for TermsBranch.
type TermsBranch struct {
	CreatedAt   *string       `json:"createdAt,omitempty"`
	Id          *string       `json:"id,omitempty"`
	PublishedAt *string       `json:"publishedAt,omitempty"`
	Status      *MasterStatus `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	Terms       *[]Terms      `json:"terms,omitempty"`
	Type        *TermsType    `json:"type,omitempty"`
	UpdatedAt   *string       `json:"updatedAt,omitempty"`
}

// TermsType defines model for TermsType.
type TermsType string

// TransferFee defines model for TransferFee.
type TransferFee struct {
	DisplayName  *map[string]string `json:"displayName,omitempty"`
	LanguageCode *string            `json:"languageCode,omitempty"`
	Price        *float64           `json:"price,omitempty"`
}

// TransferFeesBranch defines model for TransferFeesBranch.
type TransferFeesBranch struct {
	CreatedAt    *string        `json:"createdAt,omitempty"`
	Id           *string        `json:"id,omitempty"`
	PublishedAt  *string        `json:"publishedAt,omitempty"`
	Status       *MasterStatus  `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	TransferFees *[]TransferFee `json:"transferFees,omitempty"`
	UpdatedAt    *string        `json:"updatedAt,omitempty"`
}

// UiWords defines model for UiWords.
type UiWords struct {
	Key   *string            `json:"key,omitempty"`
	Value *map[string]string `json:"value,omitempty"`
}

// UiWordsBranch defines model for UiWordsBranch.
type UiWordsBranch struct {
	CreatedAt   *string       `json:"createdAt,omitempty"`
	Id          *string       `json:"id,omitempty"`
	PublishedAt *string       `json:"publishedAt,omitempty"`
	Status      *MasterStatus `json:"status,omitempty" validate:"omitempty,oneof=draft waitingToPublish published"`
	UiWordsCsv  *UiWordsCsv   `json:"ui_words_csv,omitempty"`
	UpdatedAt   *string       `json:"updatedAt,omitempty"`
}

// UiWordsCsv defines model for UiWordsCsv.
type UiWordsCsv struct {
	Count     int       `json:"count"`
	FileName  string    `json:"fileName"`
	Items     []UiWords `json:"items"`
	Languages []string  `json:"languages"`
	Path      string    `json:"path"`
}

// UploadFileRequest defines model for UploadFileRequest.
type UploadFileRequest struct {
	// File The file to upload
	File *openapi_types.File `json:"file,omitempty"`
}

// UploadFileResponse defines model for UploadFileResponse.
type UploadFileResponse struct {
	// Key The key or path of the file
	Key string `json:"key"`
}

// User defines model for User.
type User struct {
	AccountId *string `json:"accountId,omitempty"`

	// Avatar User's avatar URL
	Avatar *string `json:"avatar,omitempty"`

	// CountryCode Country code of the user
	CountryCode *string `json:"countryCode,omitempty"`

	// DateOfBirth Date of birth of the user. Format: YYYY-MM-DD
	DateOfBirth *string `json:"dateOfBirth,omitempty"`

	// Email User's email address
	Email *string `json:"email,omitempty"`

	// FirstName First name of the user
	FirstName *string `json:"firstName,omitempty"`

	// Gender Gender of the user
	Gender *string `json:"gender,omitempty"`

	// Id User's unique identifier
	Id *string `json:"id,omitempty"`

	// LastName Last name of the user
	LastName   *string `json:"lastName,omitempty"`
	Membership *int    `json:"membership,omitempty"`

	// Nickname Nickname of the user
	Nickname *string `json:"nickname,omitempty"`

	// PinSetting Whether the user has set a PIN
	PinSetting *bool `json:"pinSetting,omitempty"`

	// ReceiveNewsletter Whether the user wants to receive newsletter
	ReceiveNewsletter *bool `json:"receiveNewsletter,omitempty"`

	// RegionId Region ID of the user
	RegionId *string `json:"regionId,omitempty"`

	// Seller Comprehensive seller profile model representing a user's seller account, including associated user information, store details, and seller-specific metadata
	Seller *Seller `json:"seller,omitempty"`

	// SellerId Seller ID of the user
	SellerId *string        `json:"sellerId,omitempty"`
	Status   *AccountStatus `json:"status,omitempty"`
}

// UserStatistics User statistics summary containing aggregated data
type UserStatistics struct {
	// ActiveUsers Number of active users
	ActiveUsers int `json:"activeUsers"`

	// CountryBreakdown Statistics breakdown by country (only populated for global summary)
	CountryBreakdown *[]CountryStatistics `json:"countryBreakdown,omitempty"`

	// DisplayName Display name
	DisplayName *map[string]string `json:"displayName,omitempty"`

	// GeneralUsers Number of general users
	GeneralUsers int `json:"generalUsers"`

	// PremiumUsers Number of premium users
	PremiumUsers int `json:"premiumUsers"`

	// RegionBreakdown Statistics breakdown by region (only populated for country/region summaries)
	RegionBreakdown *[]RegionStatistics `json:"regionBreakdown,omitempty"`

	// SellerUsers Number of seller users
	SellerUsers int `json:"sellerUsers"`

	// TotalUsers Total number of users
	TotalUsers int `json:"totalUsers"`
}

// UserStatusUpdateRequest User status update request object
type UserStatusUpdateRequest struct {
	Status AccountStatus `json:"status"`
}

// UserUpdateRequest User request update object
type UserUpdateRequest struct {
	BillingAddress *Address    `json:"billingAddress,omitempty"`
	CountryCode    *string     `json:"countryCode,omitempty"`
	CreditCard     *CreditCard `json:"creditCard,omitempty"`

	// DateOfBirth Date of birth of the user. Format: YYYY-MM-DD
	DateOfBirth *string `json:"dateOfBirth,omitempty"`

	// Email User's email address
	Email *string `json:"email,omitempty"`

	// FirstName First name of the user
	FirstName *string `json:"firstName,omitempty"`

	// Gender Gender of the user
	Gender      *string  `json:"gender,omitempty"`
	HomeAddress *Address `json:"homeAddress,omitempty"`
	Language    *string  `json:"language,omitempty"`

	// LastName Last name of the user
	LastName *string `json:"lastName,omitempty"`

	// Nickname Nickname of the user
	Nickname        *string             `json:"nickname,omitempty"`
	RegionId        *openapi_types.UUID `json:"regionId,omitempty"`
	ShippingAddress *Address            `json:"shippingAddress,omitempty"`
}

// VatObligation VAT obligation information grouped by tax rate
type VatObligation struct {
	// Amount Base amount subject to VAT
	Amount float64 `json:"amount"`

	// TaxAmount VAT amount MP must pay
	TaxAmount float64 `json:"taxAmount"`

	// TaxRate VAT rate percentage
	TaxRate float64 `json:"taxRate"`
}

// SearchBannersParams defines parameters for SearchBanners.
type SearchBannersParams struct {
	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetContentsParams defines parameters for GetContents.
type GetContentsParams struct {
	Page                *int   `form:"page,omitempty" json:"page,omitempty"`
	Limit               *int   `form:"limit,omitempty" json:"limit,omitempty"`
	ContentCategoryCode string `form:"contentCategoryCode" json:"contentCategoryCode"`
}

// GetFaqCategoriesBranchesParams defines parameters for GetFaqCategoriesBranches.
type GetFaqCategoriesBranchesParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetNotificationsParams defines parameters for GetNotifications.
type GetNotificationsParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetTermsBranchesParams defines parameters for GetTermsBranches.
type GetTermsBranchesParams struct {
	Page  *int      `form:"page,omitempty" json:"page,omitempty"`
	Limit *int      `form:"limit,omitempty" json:"limit,omitempty"`
	Type  TermsType `form:"type" json:"type"`
}

// GetCountriesParams defines parameters for GetCountries.
type GetCountriesParams struct {
	// Active Active
	Active *bool `form:"active,omitempty" json:"active,omitempty"`
}

// GetListEmailsParams defines parameters for GetListEmails.
type GetListEmailsParams struct {
	NotificationSettings *NotificationSettingsType `form:"notificationSettings,omitempty" json:"notificationSettings,omitempty"`
	RegionIds            *[]openapi_types.UUID     `form:"regionIds,omitempty" json:"regionIds,omitempty"`
	CountryCodes         *[]string                 `form:"countryCodes,omitempty" json:"countryCodes,omitempty"`
	Languages            *[]string                 `form:"languages,omitempty" json:"languages,omitempty"`
}

// GetInquiryStatsAreaServiceParams defines parameters for GetInquiryStatsAreaService.
type GetInquiryStatsAreaServiceParams struct {
	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetBrandsBranchesParams defines parameters for GetBrandsBranches.
type GetBrandsBranchesParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetCommissionSalesBranchesParams defines parameters for GetCommissionSalesBranches.
type GetCommissionSalesBranchesParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetCountryBranchesParams defines parameters for GetCountryBranches.
type GetCountryBranchesParams struct {
	CountryCode *string `form:"countryCode,omitempty" json:"countryCode,omitempty"`
	Page        *int    `form:"page,omitempty" json:"page,omitempty"`
	Limit       *int    `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetCustomNgWordsBranchesParams defines parameters for GetCustomNgWordsBranches.
type GetCustomNgWordsBranchesParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetLanguagesBranchesParams defines parameters for GetLanguagesBranches.
type GetLanguagesBranchesParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetProductCategoriesBranchesParams defines parameters for GetProductCategoriesBranches.
type GetProductCategoriesBranchesParams struct {
	Status *MasterStatus `form:"status,omitempty" json:"status,omitempty"`
	Page   *int          `form:"page,omitempty" json:"page,omitempty"`
	Limit  *int          `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetPurchaseFeesBranchesParams defines parameters for GetPurchaseFeesBranches.
type GetPurchaseFeesBranchesParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetSubscriptionFeesBranchesParams defines parameters for GetSubscriptionFeesBranches.
type GetSubscriptionFeesBranchesParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetTransferFeesBranchesParams defines parameters for GetTransferFeesBranches.
type GetTransferFeesBranchesParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetUiWordsBranchesParams defines parameters for GetUiWordsBranches.
type GetUiWordsBranchesParams struct {
	Page  *int `form:"page,omitempty" json:"page,omitempty"`
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetOrderDetailsParams defines parameters for GetOrderDetails.
type GetOrderDetailsParams struct {
	// UserId Filter by buyer user ID
	UserId *string `form:"userId,omitempty" json:"userId,omitempty"`

	// SellerId Filter by seller user ID
	SellerId *string `form:"sellerId,omitempty" json:"sellerId,omitempty"`

	// TransactionStatuses Filter by transaction status values (multiple values supported)
	TransactionStatuses *[]string `form:"transactionStatuses,omitempty" json:"transactionStatuses,omitempty"`

	// OrderStatuses Filter by order status values (multiple values supported)
	OrderStatuses *[]string `form:"orderStatuses,omitempty" json:"orderStatuses,omitempty"`

	// ProductId Filter by product ID
	ProductId *string `form:"productId,omitempty" json:"productId,omitempty"`

	// OrderId Filter by order ID
	OrderId *string `form:"orderId,omitempty" json:"orderId,omitempty"`

	// From Filter orders created from this date
	From *time.Time `form:"from,omitempty" json:"from,omitempty"`

	// To Filter orders created until this date
	To *time.Time `form:"to,omitempty" json:"to,omitempty"`

	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetOrderStatisticsParams defines parameters for GetOrderStatistics.
type GetOrderStatisticsParams struct {
	// Year The year for statistics (e.g., 2024)
	Year int `form:"year" json:"year"`

	// Month The month for statistics (1-12)
	Month int `form:"month" json:"month"`

	// CountryCode ISO3 country code for filtering (e.g., "USA", "JPN")
	CountryCode *string `form:"countryCode,omitempty" json:"countryCode,omitempty"`

	// RegionId Region ID for geographical filtering
	RegionId *string `form:"regionId,omitempty" json:"regionId,omitempty"`
}

// GetOrdersParams defines parameters for GetOrders.
type GetOrdersParams struct {
	// UserId User ID
	UserId *string    `form:"userId,omitempty" json:"userId,omitempty"`
	From   *time.Time `form:"from,omitempty" json:"from,omitempty"`
	To     *time.Time `form:"to,omitempty" json:"to,omitempty"`

	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetReportsParams defines parameters for GetReports.
type GetReportsParams struct {
	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit    *int                    `form:"limit,omitempty" json:"limit,omitempty"`
	OrderBy  *OrderBy                `form:"orderBy,omitempty" json:"orderBy,omitempty"`
	SortBy   *GetReportsParamsSortBy `form:"sortBy,omitempty" json:"sortBy,omitempty"`
	Status   *ReportStatus           `form:"status,omitempty" json:"status,omitempty"`
	ItemId   *string                 `form:"itemId,omitempty" json:"itemId,omitempty"`
	ItemType *ReportItemType         `form:"itemType,omitempty" json:"itemType,omitempty"`
}

// GetReportsParamsSortBy defines parameters for GetReports.
type GetReportsParamsSortBy string

// UpdateReportStatusJSONBody defines parameters for UpdateReportStatus.
type UpdateReportStatusJSONBody struct {
	Status *ReportStatus `json:"status,omitempty"`
}

// GetSellerProductsParams defines parameters for GetSellerProducts.
type GetSellerProductsParams struct {
	// SaleStatus Filter products by sale status.  Multiple statuses can be provided.
	SaleStatus *[]ProductSalesStatus          `form:"saleStatus,omitempty" json:"saleStatus,omitempty"`
	OrderBy    *OrderBy                       `form:"orderBy,omitempty" json:"orderBy,omitempty"`
	SortBy     *GetSellerProductsParamsSortBy `form:"sortBy,omitempty" json:"sortBy,omitempty"`

	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetSellerProductsParamsSortBy defines parameters for GetSellerProducts.
type GetSellerProductsParamsSortBy string

// GetSoldItemsParams defines parameters for GetSoldItems.
type GetSoldItemsParams struct {
	// UserId Filter sold items by specific user ID
	UserId *string `form:"userId,omitempty" json:"userId,omitempty"`

	// TransactionStatuses Filter sold items by transaction status values (multiple values supported)
	TransactionStatuses *[]string  `form:"transactionStatuses,omitempty" json:"transactionStatuses,omitempty"`
	From                *time.Time `form:"from,omitempty" json:"from,omitempty"`
	To                  *time.Time `form:"to,omitempty" json:"to,omitempty"`

	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetCountryUserSummaryParams defines parameters for GetCountryUserSummary.
type GetCountryUserSummaryParams struct {
	// CountryCode ISO3 country code (e.g., "USA", "JPN")
	CountryCode string `form:"countryCode" json:"countryCode"`
}

// GetRegionUserSummaryParams defines parameters for GetRegionUserSummary.
type GetRegionUserSummaryParams struct {
	// RegionId Unique identifier for the region
	RegionId string `form:"regionId" json:"regionId"`
}

// SearchUsersParams defines parameters for SearchUsers.
type SearchUsersParams struct {
	// AccountId The unique identifier for the user account.
	AccountId *string `form:"accountId,omitempty" json:"accountId,omitempty"`

	// Nickname The user's display name or alias.
	Nickname *string `form:"nickname,omitempty" json:"nickname,omitempty"`

	// Email The user's email address.
	Email *string `form:"email,omitempty" json:"email,omitempty"`

	// AccountType The type of user account.
	AccountType *AccountType `form:"accountType,omitempty" json:"accountType,omitempty"`

	// Status The current status of the user account.
	Status *AccountStatus `form:"status,omitempty" json:"status,omitempty"`

	// CountryCode The country of the user account.
	CountryCode *string `form:"countryCode,omitempty" json:"countryCode,omitempty"`

	// RegionId The current region of the user account.
	RegionId *string `form:"regionId,omitempty" json:"regionId,omitempty"`

	// PostalCode The postal code of the user account.
	PostalCode *string `form:"postalCode,omitempty" json:"postalCode,omitempty"`

	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetPurchasedItemsParams defines parameters for GetPurchasedItems.
type GetPurchasedItemsParams struct {
	From *time.Time `form:"from,omitempty" json:"from,omitempty"`
	To   *time.Time `form:"to,omitempty" json:"to,omitempty"`

	// Page Page number for pagination.
	Page *int `form:"page,omitempty" json:"page,omitempty"`

	// Limit Number of items per page.
	Limit *int `form:"limit,omitempty" json:"limit,omitempty"`
}

// AuthJSONRequestBody defines body for Auth for application/json ContentType.
type AuthJSONRequestBody = AuthRequest

// CreateBannerJSONRequestBody defines body for CreateBanner for application/json ContentType.
type CreateBannerJSONRequestBody = Banner

// UpdateBannerJSONRequestBody defines body for UpdateBanner for application/json ContentType.
type UpdateBannerJSONRequestBody = Banner

// CreateContentJSONRequestBody defines body for CreateContent for application/json ContentType.
type CreateContentJSONRequestBody = Content

// UpdateContentJSONRequestBody defines body for UpdateContent for application/json ContentType.
type UpdateContentJSONRequestBody = Content

// CreateFaqCategoriesBranchJSONRequestBody defines body for CreateFaqCategoriesBranch for application/json ContentType.
type CreateFaqCategoriesBranchJSONRequestBody = FaqCategoriesBranch

// CreateNotificationJSONRequestBody defines body for CreateNotification for application/json ContentType.
type CreateNotificationJSONRequestBody = Notification

// UpdateNotificationJSONRequestBody defines body for UpdateNotification for application/json ContentType.
type UpdateNotificationJSONRequestBody = Notification

// CreateTermsBranchJSONRequestBody defines body for CreateTermsBranch for application/json ContentType.
type CreateTermsBranchJSONRequestBody = TermsBranch

// UpdateTermsBranchJSONRequestBody defines body for UpdateTermsBranch for application/json ContentType.
type UpdateTermsBranchJSONRequestBody = TermsBranch

// PublishTermsBranchJSONRequestBody defines body for PublishTermsBranch for application/json ContentType.
type PublishTermsBranchJSONRequestBody = PublishTermsBranchRequest

// CreateBrandsBranchJSONRequestBody defines body for CreateBrandsBranch for application/json ContentType.
type CreateBrandsBranchJSONRequestBody = BrandsBranch

// UpdateBrandsBranchJSONRequestBody defines body for UpdateBrandsBranch for application/json ContentType.
type UpdateBrandsBranchJSONRequestBody = BrandsBranch

// PublishBrandsBranchJSONRequestBody defines body for PublishBrandsBranch for application/json ContentType.
type PublishBrandsBranchJSONRequestBody = PublishBrandsBranchRequest

// CreateCommissionSalesBranchJSONRequestBody defines body for CreateCommissionSalesBranch for application/json ContentType.
type CreateCommissionSalesBranchJSONRequestBody = CommissionSalesBranch

// UpdateCommissionSalesBranchJSONRequestBody defines body for UpdateCommissionSalesBranch for application/json ContentType.
type UpdateCommissionSalesBranchJSONRequestBody = CommissionSalesBranch

// PublishCommissionSalesBranchJSONRequestBody defines body for PublishCommissionSalesBranch for application/json ContentType.
type PublishCommissionSalesBranchJSONRequestBody = PublishCommissionSalesBranchRequest

// CreateCountryBranchJSONRequestBody defines body for CreateCountryBranch for application/json ContentType.
type CreateCountryBranchJSONRequestBody = CountryBranch

// UpdateCountryBranchJSONRequestBody defines body for UpdateCountryBranch for application/json ContentType.
type UpdateCountryBranchJSONRequestBody = CountryBranch

// PublishCountryBranchJSONRequestBody defines body for PublishCountryBranch for application/json ContentType.
type PublishCountryBranchJSONRequestBody = PublishCountryBranchRequest

// CreateCustomNgWordsBranchJSONRequestBody defines body for CreateCustomNgWordsBranch for application/json ContentType.
type CreateCustomNgWordsBranchJSONRequestBody = CustomNGWordsBranch

// UpdateCustomNgWordsBranchJSONRequestBody defines body for UpdateCustomNgWordsBranch for application/json ContentType.
type UpdateCustomNgWordsBranchJSONRequestBody = CustomNGWordsBranch

// PublishCustomNgWordsBranchJSONRequestBody defines body for PublishCustomNgWordsBranch for application/json ContentType.
type PublishCustomNgWordsBranchJSONRequestBody = PublishCustomNGWordsBranchRequest

// CreateLanguagesBranchJSONRequestBody defines body for CreateLanguagesBranch for application/json ContentType.
type CreateLanguagesBranchJSONRequestBody = LanguagesBranch

// UpdateLanguagesBranchJSONRequestBody defines body for UpdateLanguagesBranch for application/json ContentType.
type UpdateLanguagesBranchJSONRequestBody = LanguagesBranch

// PublishLanguagesBranchJSONRequestBody defines body for PublishLanguagesBranch for application/json ContentType.
type PublishLanguagesBranchJSONRequestBody = PublishLanguagesBranchRequest

// CreateProductCategoriesBranchJSONRequestBody defines body for CreateProductCategoriesBranch for application/json ContentType.
type CreateProductCategoriesBranchJSONRequestBody = ProductCategoriesBranch

// UpdateProductCategoriesBranchJSONRequestBody defines body for UpdateProductCategoriesBranch for application/json ContentType.
type UpdateProductCategoriesBranchJSONRequestBody = ProductCategoriesBranch

// PublishProductCategoriesBranchJSONRequestBody defines body for PublishProductCategoriesBranch for application/json ContentType.
type PublishProductCategoriesBranchJSONRequestBody = PublishProductCategoriesBranchRequest

// CreatePurchaseFeesBranchJSONRequestBody defines body for CreatePurchaseFeesBranch for application/json ContentType.
type CreatePurchaseFeesBranchJSONRequestBody = PurchaseFeesBranch

// UpdatePurchaseFeesBranchJSONRequestBody defines body for UpdatePurchaseFeesBranch for application/json ContentType.
type UpdatePurchaseFeesBranchJSONRequestBody = PurchaseFeesBranch

// PublishPurchaseFeesBranchJSONRequestBody defines body for PublishPurchaseFeesBranch for application/json ContentType.
type PublishPurchaseFeesBranchJSONRequestBody = PublishPurchaseFeesBranchRequest

// CreateSubscriptionFeesBranchJSONRequestBody defines body for CreateSubscriptionFeesBranch for application/json ContentType.
type CreateSubscriptionFeesBranchJSONRequestBody = SubscriptionFeesBranch

// UpdateSubscriptionFeesBranchJSONRequestBody defines body for UpdateSubscriptionFeesBranch for application/json ContentType.
type UpdateSubscriptionFeesBranchJSONRequestBody = SubscriptionFeesBranch

// PublishSubscriptionFeesBranchJSONRequestBody defines body for PublishSubscriptionFeesBranch for application/json ContentType.
type PublishSubscriptionFeesBranchJSONRequestBody = PublishSubscriptionFeesBranchRequest

// CreateTransferFeesBranchJSONRequestBody defines body for CreateTransferFeesBranch for application/json ContentType.
type CreateTransferFeesBranchJSONRequestBody = TransferFeesBranch

// UpdateTransferFeesBranchJSONRequestBody defines body for UpdateTransferFeesBranch for application/json ContentType.
type UpdateTransferFeesBranchJSONRequestBody = TransferFeesBranch

// PublishTransferFeesBranchJSONRequestBody defines body for PublishTransferFeesBranch for application/json ContentType.
type PublishTransferFeesBranchJSONRequestBody = PublishTransferFeesBranchRequest

// CreateUiWordsBranchJSONRequestBody defines body for CreateUiWordsBranch for application/json ContentType.
type CreateUiWordsBranchJSONRequestBody = UiWordsBranch

// UpdateUiWordsBranchJSONRequestBody defines body for UpdateUiWordsBranch for application/json ContentType.
type UpdateUiWordsBranchJSONRequestBody = UiWordsBranch

// PublishUiWordsBranchJSONRequestBody defines body for PublishUiWordsBranch for application/json ContentType.
type PublishUiWordsBranchJSONRequestBody = PublishUiWordsBranchRequest

// BanOrderByIdJSONRequestBody defines body for BanOrderById for application/json ContentType.
type BanOrderByIdJSONRequestBody = BanOrderRequest

// SearchProductsJSONRequestBody defines body for SearchProducts for application/json ContentType.
type SearchProductsJSONRequestBody = SearchProductsRequest

// UpdateProductByIdJSONRequestBody defines body for UpdateProductById for application/json ContentType.
type UpdateProductByIdJSONRequestBody = ProductUpdateRequest

// BanProductByIdJSONRequestBody defines body for BanProductById for application/json ContentType.
type BanProductByIdJSONRequestBody = ProductBanRequest

// UpdateReportStatusJSONRequestBody defines body for UpdateReportStatus for application/json ContentType.
type UpdateReportStatusJSONRequestBody UpdateReportStatusJSONBody

// UpdateMaintenanceStatusJSONRequestBody defines body for UpdateMaintenanceStatus for application/json ContentType.
type UpdateMaintenanceStatusJSONRequestBody = MaintenanceUpdateRequest

// UploadFileMultipartRequestBody defines body for UploadFile for multipart/form-data ContentType.
type UploadFileMultipartRequestBody = UploadFileRequest

// UpdateUserByIdJSONRequestBody defines body for UpdateUserById for application/json ContentType.
type UpdateUserByIdJSONRequestBody = UserUpdateRequest

// CreateUserNotificationJSONRequestBody defines body for CreateUserNotification for application/json ContentType.
type CreateUserNotificationJSONRequestBody = CreateUserNotificationRequest

// UpdateUserStatusJSONRequestBody defines body for UpdateUserStatus for application/json ContentType.
type UpdateUserStatusJSONRequestBody = UserStatusUpdateRequest

// AsProduct returns the union data inside the Report_Item as a Product
func (t Report_Item) AsProduct() (Product, error) {
	var body Product
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromProduct overwrites any union data inside the Report_Item as the provided Product
func (t *Report_Item) FromProduct(v Product) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeProduct performs a merge with any union data inside the Report_Item, using the provided Product
func (t *Report_Item) MergeProduct(v Product) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsUser returns the union data inside the Report_Item as a User
func (t Report_Item) AsUser() (User, error) {
	var body User
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromUser overwrites any union data inside the Report_Item as the provided User
func (t *Report_Item) FromUser(v User) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeUser performs a merge with any union data inside the Report_Item, using the provided User
func (t *Report_Item) MergeUser(v User) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsSeller returns the union data inside the Report_Item as a Seller
func (t Report_Item) AsSeller() (Seller, error) {
	var body Seller
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromSeller overwrites any union data inside the Report_Item as the provided Seller
func (t *Report_Item) FromSeller(v Seller) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeSeller performs a merge with any union data inside the Report_Item, using the provided Seller
func (t *Report_Item) MergeSeller(v Seller) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t Report_Item) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *Report_Item) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// AsUser returns the union data inside the Report_Reporter as a User
func (t Report_Reporter) AsUser() (User, error) {
	var body User
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromUser overwrites any union data inside the Report_Reporter as the provided User
func (t *Report_Reporter) FromUser(v User) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeUser performs a merge with any union data inside the Report_Reporter, using the provided User
func (t *Report_Reporter) MergeUser(v User) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsSeller returns the union data inside the Report_Reporter as a Seller
func (t Report_Reporter) AsSeller() (Seller, error) {
	var body Seller
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromSeller overwrites any union data inside the Report_Reporter as the provided Seller
func (t *Report_Reporter) FromSeller(v Seller) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeSeller performs a merge with any union data inside the Report_Reporter, using the provided Seller
func (t *Report_Reporter) MergeSeller(v Seller) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t Report_Reporter) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *Report_Reporter) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}
