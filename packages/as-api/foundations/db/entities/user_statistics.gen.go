// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entities

import (
	"time"
)

const TableNameUserStatistic = "user_statistics"

// UserStatistic mapped from table <user_statistics>
type UserStatistic struct {
	ID          *string    `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`
	CreatedAt   *time.Time `gorm:"column:created_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt   *time.Time `gorm:"column:updated_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"updated_at"`
	Country     *string    `gorm:"column:country;type:character varying(255)" json:"country"`
	RegionID    *string    `gorm:"column:region_id;type:uuid" json:"region_id"`
	UserType    string     `gorm:"column:user_type;type:character varying(50);not null" json:"user_type"`
	TotalCount  int32      `gorm:"column:total_count;type:integer;not null" json:"total_count"`
	ActiveCount int32      `gorm:"column:active_count;type:integer;not null" json:"active_count"`
}

// TableName UserStatistic's table name
func (*UserStatistic) TableName() string {
	return TableNameUserStatistic
}
