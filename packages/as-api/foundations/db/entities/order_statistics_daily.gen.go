// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entities

import (
	"time"
)

const TableNameOrderStatisticsDaily = "order_statistics_daily"

// OrderStatisticsDaily mapped from table <order_statistics_daily>
type OrderStatisticsDaily struct {
	ID                   *string    `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`
	CreatedAt            *time.Time `gorm:"column:created_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt            *time.Time `gorm:"column:updated_at;type:timestamp without time zone;default:CURRENT_TIMESTAMP" json:"updated_at"`
	Year                 int32      `gorm:"column:year;type:integer;not null" json:"year"`
	Month                int32      `gorm:"column:month;type:integer;not null" json:"month"`
	Day                  int32      `gorm:"column:day;type:integer;not null" json:"day"`
	CountryCode          *string    `gorm:"column:country_code;type:character varying(255)" json:"country_code"`
	RegionID             *string    `gorm:"column:region_id;type:uuid" json:"region_id"`
	OrderCount           *int32     `gorm:"column:order_count;type:integer" json:"order_count"`
	OrderAmount          *float64   `gorm:"column:order_amount;type:numeric(15,2);default:0.00" json:"order_amount"`
	SalesAmount          *float64   `gorm:"column:sales_amount;type:numeric(15,2);default:0.00" json:"sales_amount"`
	ProductPrice         *float64   `gorm:"column:product_price;type:numeric(15,2);default:0.00" json:"product_price"`
	VatSeller            *JSON      `gorm:"column:vat_seller;type:jsonb" json:"vat_seller"`
	VatMp                *JSON      `gorm:"column:vat_mp;type:jsonb" json:"vat_mp"`
	MemberDiscountAmount *float64   `gorm:"column:member_discount_amount;type:numeric(15,2);default:0.00" json:"member_discount_amount"`
	SalesFeesAmount      *float64   `gorm:"column:sales_fees_amount;type:numeric(15,2);default:0.00" json:"sales_fees_amount"`
	PurchaseFeesAmount   *float64   `gorm:"column:purchase_fees_amount;type:numeric(15,2);default:0.00" json:"purchase_fees_amount"`
}

// TableName OrderStatisticsDaily's table name
func (*OrderStatisticsDaily) TableName() string {
	return TableNameOrderStatisticsDaily
}
