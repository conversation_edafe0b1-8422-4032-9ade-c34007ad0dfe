// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"as-api/as/foundations/db/entities"
)

func newOrderStatisticsDaily(db *gorm.DB, opts ...gen.DOOption) orderStatisticsDaily {
	_orderStatisticsDaily := orderStatisticsDaily{}

	_orderStatisticsDaily.orderStatisticsDailyDo.UseDB(db, opts...)
	_orderStatisticsDaily.orderStatisticsDailyDo.UseModel(&entities.OrderStatisticsDaily{})

	tableName := _orderStatisticsDaily.orderStatisticsDailyDo.TableName()
	_orderStatisticsDaily.ALL = field.NewAsterisk(tableName)
	_orderStatisticsDaily.ID = field.NewString(tableName, "id")
	_orderStatisticsDaily.CreatedAt = field.NewTime(tableName, "created_at")
	_orderStatisticsDaily.UpdatedAt = field.NewTime(tableName, "updated_at")
	_orderStatisticsDaily.Year = field.NewInt32(tableName, "year")
	_orderStatisticsDaily.Month = field.NewInt32(tableName, "month")
	_orderStatisticsDaily.Day = field.NewInt32(tableName, "day")
	_orderStatisticsDaily.CountryCode = field.NewString(tableName, "country_code")
	_orderStatisticsDaily.RegionID = field.NewString(tableName, "region_id")
	_orderStatisticsDaily.OrderCount = field.NewInt32(tableName, "order_count")
	_orderStatisticsDaily.OrderAmount = field.NewFloat64(tableName, "order_amount")
	_orderStatisticsDaily.SalesAmount = field.NewFloat64(tableName, "sales_amount")
	_orderStatisticsDaily.ProductPrice = field.NewFloat64(tableName, "product_price")
	_orderStatisticsDaily.VatSeller = field.NewField(tableName, "vat_seller")
	_orderStatisticsDaily.VatMp = field.NewField(tableName, "vat_mp")
	_orderStatisticsDaily.MemberDiscountAmount = field.NewFloat64(tableName, "member_discount_amount")
	_orderStatisticsDaily.SalesFeesAmount = field.NewFloat64(tableName, "sales_fees_amount")
	_orderStatisticsDaily.PurchaseFeesAmount = field.NewFloat64(tableName, "purchase_fees_amount")

	_orderStatisticsDaily.fillFieldMap()

	return _orderStatisticsDaily
}

type orderStatisticsDaily struct {
	orderStatisticsDailyDo

	ALL                  field.Asterisk
	ID                   field.String
	CreatedAt            field.Time
	UpdatedAt            field.Time
	Year                 field.Int32
	Month                field.Int32
	Day                  field.Int32
	CountryCode          field.String
	RegionID             field.String
	OrderCount           field.Int32
	OrderAmount          field.Float64
	SalesAmount          field.Float64
	ProductPrice         field.Float64
	VatSeller            field.Field
	VatMp                field.Field
	MemberDiscountAmount field.Float64
	SalesFeesAmount      field.Float64
	PurchaseFeesAmount   field.Float64

	fieldMap map[string]field.Expr
}

func (o orderStatisticsDaily) Table(newTableName string) *orderStatisticsDaily {
	o.orderStatisticsDailyDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o orderStatisticsDaily) As(alias string) *orderStatisticsDaily {
	o.orderStatisticsDailyDo.DO = *(o.orderStatisticsDailyDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *orderStatisticsDaily) updateTableName(table string) *orderStatisticsDaily {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewString(table, "id")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")
	o.Year = field.NewInt32(table, "year")
	o.Month = field.NewInt32(table, "month")
	o.Day = field.NewInt32(table, "day")
	o.CountryCode = field.NewString(table, "country_code")
	o.RegionID = field.NewString(table, "region_id")
	o.OrderCount = field.NewInt32(table, "order_count")
	o.OrderAmount = field.NewFloat64(table, "order_amount")
	o.SalesAmount = field.NewFloat64(table, "sales_amount")
	o.ProductPrice = field.NewFloat64(table, "product_price")
	o.VatSeller = field.NewField(table, "vat_seller")
	o.VatMp = field.NewField(table, "vat_mp")
	o.MemberDiscountAmount = field.NewFloat64(table, "member_discount_amount")
	o.SalesFeesAmount = field.NewFloat64(table, "sales_fees_amount")
	o.PurchaseFeesAmount = field.NewFloat64(table, "purchase_fees_amount")

	o.fillFieldMap()

	return o
}

func (o *orderStatisticsDaily) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *orderStatisticsDaily) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 17)
	o.fieldMap["id"] = o.ID
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["year"] = o.Year
	o.fieldMap["month"] = o.Month
	o.fieldMap["day"] = o.Day
	o.fieldMap["country_code"] = o.CountryCode
	o.fieldMap["region_id"] = o.RegionID
	o.fieldMap["order_count"] = o.OrderCount
	o.fieldMap["order_amount"] = o.OrderAmount
	o.fieldMap["sales_amount"] = o.SalesAmount
	o.fieldMap["product_price"] = o.ProductPrice
	o.fieldMap["vat_seller"] = o.VatSeller
	o.fieldMap["vat_mp"] = o.VatMp
	o.fieldMap["member_discount_amount"] = o.MemberDiscountAmount
	o.fieldMap["sales_fees_amount"] = o.SalesFeesAmount
	o.fieldMap["purchase_fees_amount"] = o.PurchaseFeesAmount
}

func (o orderStatisticsDaily) clone(db *gorm.DB) orderStatisticsDaily {
	o.orderStatisticsDailyDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o orderStatisticsDaily) replaceDB(db *gorm.DB) orderStatisticsDaily {
	o.orderStatisticsDailyDo.ReplaceDB(db)
	return o
}

type orderStatisticsDailyDo struct{ gen.DO }

type IOrderStatisticsDailyDo interface {
	gen.SubQuery
	Debug() IOrderStatisticsDailyDo
	WithContext(ctx context.Context) IOrderStatisticsDailyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IOrderStatisticsDailyDo
	WriteDB() IOrderStatisticsDailyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IOrderStatisticsDailyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IOrderStatisticsDailyDo
	Not(conds ...gen.Condition) IOrderStatisticsDailyDo
	Or(conds ...gen.Condition) IOrderStatisticsDailyDo
	Select(conds ...field.Expr) IOrderStatisticsDailyDo
	Where(conds ...gen.Condition) IOrderStatisticsDailyDo
	Order(conds ...field.Expr) IOrderStatisticsDailyDo
	Distinct(cols ...field.Expr) IOrderStatisticsDailyDo
	Omit(cols ...field.Expr) IOrderStatisticsDailyDo
	Join(table schema.Tabler, on ...field.Expr) IOrderStatisticsDailyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IOrderStatisticsDailyDo
	RightJoin(table schema.Tabler, on ...field.Expr) IOrderStatisticsDailyDo
	Group(cols ...field.Expr) IOrderStatisticsDailyDo
	Having(conds ...gen.Condition) IOrderStatisticsDailyDo
	Limit(limit int) IOrderStatisticsDailyDo
	Offset(offset int) IOrderStatisticsDailyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IOrderStatisticsDailyDo
	Unscoped() IOrderStatisticsDailyDo
	Create(values ...*entities.OrderStatisticsDaily) error
	CreateInBatches(values []*entities.OrderStatisticsDaily, batchSize int) error
	Save(values ...*entities.OrderStatisticsDaily) error
	First() (*entities.OrderStatisticsDaily, error)
	Take() (*entities.OrderStatisticsDaily, error)
	Last() (*entities.OrderStatisticsDaily, error)
	Find() ([]*entities.OrderStatisticsDaily, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.OrderStatisticsDaily, err error)
	FindInBatches(result *[]*entities.OrderStatisticsDaily, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entities.OrderStatisticsDaily) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IOrderStatisticsDailyDo
	Assign(attrs ...field.AssignExpr) IOrderStatisticsDailyDo
	Joins(fields ...field.RelationField) IOrderStatisticsDailyDo
	Preload(fields ...field.RelationField) IOrderStatisticsDailyDo
	FirstOrInit() (*entities.OrderStatisticsDaily, error)
	FirstOrCreate() (*entities.OrderStatisticsDaily, error)
	FindByPage(offset int, limit int) (result []*entities.OrderStatisticsDaily, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IOrderStatisticsDailyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (o orderStatisticsDailyDo) Debug() IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Debug())
}

func (o orderStatisticsDailyDo) WithContext(ctx context.Context) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o orderStatisticsDailyDo) ReadDB() IOrderStatisticsDailyDo {
	return o.Clauses(dbresolver.Read)
}

func (o orderStatisticsDailyDo) WriteDB() IOrderStatisticsDailyDo {
	return o.Clauses(dbresolver.Write)
}

func (o orderStatisticsDailyDo) Session(config *gorm.Session) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Session(config))
}

func (o orderStatisticsDailyDo) Clauses(conds ...clause.Expression) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o orderStatisticsDailyDo) Returning(value interface{}, columns ...string) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o orderStatisticsDailyDo) Not(conds ...gen.Condition) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o orderStatisticsDailyDo) Or(conds ...gen.Condition) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o orderStatisticsDailyDo) Select(conds ...field.Expr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o orderStatisticsDailyDo) Where(conds ...gen.Condition) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o orderStatisticsDailyDo) Order(conds ...field.Expr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o orderStatisticsDailyDo) Distinct(cols ...field.Expr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o orderStatisticsDailyDo) Omit(cols ...field.Expr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o orderStatisticsDailyDo) Join(table schema.Tabler, on ...field.Expr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o orderStatisticsDailyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o orderStatisticsDailyDo) RightJoin(table schema.Tabler, on ...field.Expr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o orderStatisticsDailyDo) Group(cols ...field.Expr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o orderStatisticsDailyDo) Having(conds ...gen.Condition) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o orderStatisticsDailyDo) Limit(limit int) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o orderStatisticsDailyDo) Offset(offset int) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o orderStatisticsDailyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o orderStatisticsDailyDo) Unscoped() IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Unscoped())
}

func (o orderStatisticsDailyDo) Create(values ...*entities.OrderStatisticsDaily) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o orderStatisticsDailyDo) CreateInBatches(values []*entities.OrderStatisticsDaily, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o orderStatisticsDailyDo) Save(values ...*entities.OrderStatisticsDaily) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o orderStatisticsDailyDo) First() (*entities.OrderStatisticsDaily, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsDaily), nil
	}
}

func (o orderStatisticsDailyDo) Take() (*entities.OrderStatisticsDaily, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsDaily), nil
	}
}

func (o orderStatisticsDailyDo) Last() (*entities.OrderStatisticsDaily, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsDaily), nil
	}
}

func (o orderStatisticsDailyDo) Find() ([]*entities.OrderStatisticsDaily, error) {
	result, err := o.DO.Find()
	return result.([]*entities.OrderStatisticsDaily), err
}

func (o orderStatisticsDailyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.OrderStatisticsDaily, err error) {
	buf := make([]*entities.OrderStatisticsDaily, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o orderStatisticsDailyDo) FindInBatches(result *[]*entities.OrderStatisticsDaily, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o orderStatisticsDailyDo) Attrs(attrs ...field.AssignExpr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o orderStatisticsDailyDo) Assign(attrs ...field.AssignExpr) IOrderStatisticsDailyDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o orderStatisticsDailyDo) Joins(fields ...field.RelationField) IOrderStatisticsDailyDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o orderStatisticsDailyDo) Preload(fields ...field.RelationField) IOrderStatisticsDailyDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o orderStatisticsDailyDo) FirstOrInit() (*entities.OrderStatisticsDaily, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsDaily), nil
	}
}

func (o orderStatisticsDailyDo) FirstOrCreate() (*entities.OrderStatisticsDaily, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsDaily), nil
	}
}

func (o orderStatisticsDailyDo) FindByPage(offset int, limit int) (result []*entities.OrderStatisticsDaily, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o orderStatisticsDailyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o orderStatisticsDailyDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o orderStatisticsDailyDo) Delete(models ...*entities.OrderStatisticsDaily) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *orderStatisticsDailyDo) withDO(do gen.Dao) *orderStatisticsDailyDo {
	o.DO = *do.(*gen.DO)
	return o
}
