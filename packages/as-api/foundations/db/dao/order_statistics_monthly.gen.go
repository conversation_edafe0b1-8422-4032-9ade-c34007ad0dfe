// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"as-api/as/foundations/db/entities"
)

func newOrderStatisticsMonthly(db *gorm.DB, opts ...gen.DOOption) orderStatisticsMonthly {
	_orderStatisticsMonthly := orderStatisticsMonthly{}

	_orderStatisticsMonthly.orderStatisticsMonthlyDo.UseDB(db, opts...)
	_orderStatisticsMonthly.orderStatisticsMonthlyDo.UseModel(&entities.OrderStatisticsMonthly{})

	tableName := _orderStatisticsMonthly.orderStatisticsMonthlyDo.TableName()
	_orderStatisticsMonthly.ALL = field.NewAsterisk(tableName)
	_orderStatisticsMonthly.ID = field.NewString(tableName, "id")
	_orderStatisticsMonthly.CreatedAt = field.NewTime(tableName, "created_at")
	_orderStatisticsMonthly.UpdatedAt = field.NewTime(tableName, "updated_at")
	_orderStatisticsMonthly.Year = field.NewInt32(tableName, "year")
	_orderStatisticsMonthly.Month = field.NewInt32(tableName, "month")
	_orderStatisticsMonthly.CountryCode = field.NewString(tableName, "country_code")
	_orderStatisticsMonthly.RegionID = field.NewString(tableName, "region_id")
	_orderStatisticsMonthly.OrderCount = field.NewInt32(tableName, "order_count")
	_orderStatisticsMonthly.OrderAmount = field.NewFloat64(tableName, "order_amount")
	_orderStatisticsMonthly.SalesAmount = field.NewFloat64(tableName, "sales_amount")
	_orderStatisticsMonthly.ProductPrice = field.NewFloat64(tableName, "product_price")
	_orderStatisticsMonthly.VatSeller = field.NewField(tableName, "vat_seller")
	_orderStatisticsMonthly.VatMp = field.NewField(tableName, "vat_mp")
	_orderStatisticsMonthly.MemberDiscountAmount = field.NewFloat64(tableName, "member_discount_amount")
	_orderStatisticsMonthly.SalesFeesAmount = field.NewFloat64(tableName, "sales_fees_amount")
	_orderStatisticsMonthly.PurchaseFeesAmount = field.NewFloat64(tableName, "purchase_fees_amount")

	_orderStatisticsMonthly.fillFieldMap()

	return _orderStatisticsMonthly
}

type orderStatisticsMonthly struct {
	orderStatisticsMonthlyDo

	ALL                  field.Asterisk
	ID                   field.String
	CreatedAt            field.Time
	UpdatedAt            field.Time
	Year                 field.Int32
	Month                field.Int32
	CountryCode          field.String
	RegionID             field.String
	OrderCount           field.Int32
	OrderAmount          field.Float64
	SalesAmount          field.Float64
	ProductPrice         field.Float64
	VatSeller            field.Field
	VatMp                field.Field
	MemberDiscountAmount field.Float64
	SalesFeesAmount      field.Float64
	PurchaseFeesAmount   field.Float64

	fieldMap map[string]field.Expr
}

func (o orderStatisticsMonthly) Table(newTableName string) *orderStatisticsMonthly {
	o.orderStatisticsMonthlyDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o orderStatisticsMonthly) As(alias string) *orderStatisticsMonthly {
	o.orderStatisticsMonthlyDo.DO = *(o.orderStatisticsMonthlyDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *orderStatisticsMonthly) updateTableName(table string) *orderStatisticsMonthly {
	o.ALL = field.NewAsterisk(table)
	o.ID = field.NewString(table, "id")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")
	o.Year = field.NewInt32(table, "year")
	o.Month = field.NewInt32(table, "month")
	o.CountryCode = field.NewString(table, "country_code")
	o.RegionID = field.NewString(table, "region_id")
	o.OrderCount = field.NewInt32(table, "order_count")
	o.OrderAmount = field.NewFloat64(table, "order_amount")
	o.SalesAmount = field.NewFloat64(table, "sales_amount")
	o.ProductPrice = field.NewFloat64(table, "product_price")
	o.VatSeller = field.NewField(table, "vat_seller")
	o.VatMp = field.NewField(table, "vat_mp")
	o.MemberDiscountAmount = field.NewFloat64(table, "member_discount_amount")
	o.SalesFeesAmount = field.NewFloat64(table, "sales_fees_amount")
	o.PurchaseFeesAmount = field.NewFloat64(table, "purchase_fees_amount")

	o.fillFieldMap()

	return o
}

func (o *orderStatisticsMonthly) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *orderStatisticsMonthly) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 16)
	o.fieldMap["id"] = o.ID
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["year"] = o.Year
	o.fieldMap["month"] = o.Month
	o.fieldMap["country_code"] = o.CountryCode
	o.fieldMap["region_id"] = o.RegionID
	o.fieldMap["order_count"] = o.OrderCount
	o.fieldMap["order_amount"] = o.OrderAmount
	o.fieldMap["sales_amount"] = o.SalesAmount
	o.fieldMap["product_price"] = o.ProductPrice
	o.fieldMap["vat_seller"] = o.VatSeller
	o.fieldMap["vat_mp"] = o.VatMp
	o.fieldMap["member_discount_amount"] = o.MemberDiscountAmount
	o.fieldMap["sales_fees_amount"] = o.SalesFeesAmount
	o.fieldMap["purchase_fees_amount"] = o.PurchaseFeesAmount
}

func (o orderStatisticsMonthly) clone(db *gorm.DB) orderStatisticsMonthly {
	o.orderStatisticsMonthlyDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o orderStatisticsMonthly) replaceDB(db *gorm.DB) orderStatisticsMonthly {
	o.orderStatisticsMonthlyDo.ReplaceDB(db)
	return o
}

type orderStatisticsMonthlyDo struct{ gen.DO }

type IOrderStatisticsMonthlyDo interface {
	gen.SubQuery
	Debug() IOrderStatisticsMonthlyDo
	WithContext(ctx context.Context) IOrderStatisticsMonthlyDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IOrderStatisticsMonthlyDo
	WriteDB() IOrderStatisticsMonthlyDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IOrderStatisticsMonthlyDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IOrderStatisticsMonthlyDo
	Not(conds ...gen.Condition) IOrderStatisticsMonthlyDo
	Or(conds ...gen.Condition) IOrderStatisticsMonthlyDo
	Select(conds ...field.Expr) IOrderStatisticsMonthlyDo
	Where(conds ...gen.Condition) IOrderStatisticsMonthlyDo
	Order(conds ...field.Expr) IOrderStatisticsMonthlyDo
	Distinct(cols ...field.Expr) IOrderStatisticsMonthlyDo
	Omit(cols ...field.Expr) IOrderStatisticsMonthlyDo
	Join(table schema.Tabler, on ...field.Expr) IOrderStatisticsMonthlyDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IOrderStatisticsMonthlyDo
	RightJoin(table schema.Tabler, on ...field.Expr) IOrderStatisticsMonthlyDo
	Group(cols ...field.Expr) IOrderStatisticsMonthlyDo
	Having(conds ...gen.Condition) IOrderStatisticsMonthlyDo
	Limit(limit int) IOrderStatisticsMonthlyDo
	Offset(offset int) IOrderStatisticsMonthlyDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IOrderStatisticsMonthlyDo
	Unscoped() IOrderStatisticsMonthlyDo
	Create(values ...*entities.OrderStatisticsMonthly) error
	CreateInBatches(values []*entities.OrderStatisticsMonthly, batchSize int) error
	Save(values ...*entities.OrderStatisticsMonthly) error
	First() (*entities.OrderStatisticsMonthly, error)
	Take() (*entities.OrderStatisticsMonthly, error)
	Last() (*entities.OrderStatisticsMonthly, error)
	Find() ([]*entities.OrderStatisticsMonthly, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.OrderStatisticsMonthly, err error)
	FindInBatches(result *[]*entities.OrderStatisticsMonthly, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entities.OrderStatisticsMonthly) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IOrderStatisticsMonthlyDo
	Assign(attrs ...field.AssignExpr) IOrderStatisticsMonthlyDo
	Joins(fields ...field.RelationField) IOrderStatisticsMonthlyDo
	Preload(fields ...field.RelationField) IOrderStatisticsMonthlyDo
	FirstOrInit() (*entities.OrderStatisticsMonthly, error)
	FirstOrCreate() (*entities.OrderStatisticsMonthly, error)
	FindByPage(offset int, limit int) (result []*entities.OrderStatisticsMonthly, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IOrderStatisticsMonthlyDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (o orderStatisticsMonthlyDo) Debug() IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Debug())
}

func (o orderStatisticsMonthlyDo) WithContext(ctx context.Context) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o orderStatisticsMonthlyDo) ReadDB() IOrderStatisticsMonthlyDo {
	return o.Clauses(dbresolver.Read)
}

func (o orderStatisticsMonthlyDo) WriteDB() IOrderStatisticsMonthlyDo {
	return o.Clauses(dbresolver.Write)
}

func (o orderStatisticsMonthlyDo) Session(config *gorm.Session) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Session(config))
}

func (o orderStatisticsMonthlyDo) Clauses(conds ...clause.Expression) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o orderStatisticsMonthlyDo) Returning(value interface{}, columns ...string) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o orderStatisticsMonthlyDo) Not(conds ...gen.Condition) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o orderStatisticsMonthlyDo) Or(conds ...gen.Condition) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o orderStatisticsMonthlyDo) Select(conds ...field.Expr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o orderStatisticsMonthlyDo) Where(conds ...gen.Condition) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o orderStatisticsMonthlyDo) Order(conds ...field.Expr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o orderStatisticsMonthlyDo) Distinct(cols ...field.Expr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o orderStatisticsMonthlyDo) Omit(cols ...field.Expr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o orderStatisticsMonthlyDo) Join(table schema.Tabler, on ...field.Expr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o orderStatisticsMonthlyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o orderStatisticsMonthlyDo) RightJoin(table schema.Tabler, on ...field.Expr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o orderStatisticsMonthlyDo) Group(cols ...field.Expr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o orderStatisticsMonthlyDo) Having(conds ...gen.Condition) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o orderStatisticsMonthlyDo) Limit(limit int) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o orderStatisticsMonthlyDo) Offset(offset int) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o orderStatisticsMonthlyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o orderStatisticsMonthlyDo) Unscoped() IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Unscoped())
}

func (o orderStatisticsMonthlyDo) Create(values ...*entities.OrderStatisticsMonthly) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o orderStatisticsMonthlyDo) CreateInBatches(values []*entities.OrderStatisticsMonthly, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o orderStatisticsMonthlyDo) Save(values ...*entities.OrderStatisticsMonthly) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o orderStatisticsMonthlyDo) First() (*entities.OrderStatisticsMonthly, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsMonthly), nil
	}
}

func (o orderStatisticsMonthlyDo) Take() (*entities.OrderStatisticsMonthly, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsMonthly), nil
	}
}

func (o orderStatisticsMonthlyDo) Last() (*entities.OrderStatisticsMonthly, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsMonthly), nil
	}
}

func (o orderStatisticsMonthlyDo) Find() ([]*entities.OrderStatisticsMonthly, error) {
	result, err := o.DO.Find()
	return result.([]*entities.OrderStatisticsMonthly), err
}

func (o orderStatisticsMonthlyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.OrderStatisticsMonthly, err error) {
	buf := make([]*entities.OrderStatisticsMonthly, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o orderStatisticsMonthlyDo) FindInBatches(result *[]*entities.OrderStatisticsMonthly, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o orderStatisticsMonthlyDo) Attrs(attrs ...field.AssignExpr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o orderStatisticsMonthlyDo) Assign(attrs ...field.AssignExpr) IOrderStatisticsMonthlyDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o orderStatisticsMonthlyDo) Joins(fields ...field.RelationField) IOrderStatisticsMonthlyDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o orderStatisticsMonthlyDo) Preload(fields ...field.RelationField) IOrderStatisticsMonthlyDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o orderStatisticsMonthlyDo) FirstOrInit() (*entities.OrderStatisticsMonthly, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsMonthly), nil
	}
}

func (o orderStatisticsMonthlyDo) FirstOrCreate() (*entities.OrderStatisticsMonthly, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entities.OrderStatisticsMonthly), nil
	}
}

func (o orderStatisticsMonthlyDo) FindByPage(offset int, limit int) (result []*entities.OrderStatisticsMonthly, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o orderStatisticsMonthlyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o orderStatisticsMonthlyDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o orderStatisticsMonthlyDo) Delete(models ...*entities.OrderStatisticsMonthly) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *orderStatisticsMonthlyDo) withDO(do gen.Dao) *orderStatisticsMonthlyDo {
	o.DO = *do.(*gen.DO)
	return o
}
