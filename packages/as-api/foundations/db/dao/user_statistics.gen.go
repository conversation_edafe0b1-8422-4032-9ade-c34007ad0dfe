// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dao

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"as-api/as/foundations/db/entities"
)

func newUserStatistic(db *gorm.DB, opts ...gen.DOOption) userStatistic {
	_userStatistic := userStatistic{}

	_userStatistic.userStatisticDo.UseDB(db, opts...)
	_userStatistic.userStatisticDo.UseModel(&entities.UserStatistic{})

	tableName := _userStatistic.userStatisticDo.TableName()
	_userStatistic.ALL = field.NewAsterisk(tableName)
	_userStatistic.ID = field.NewString(tableName, "id")
	_userStatistic.CreatedAt = field.NewTime(tableName, "created_at")
	_userStatistic.UpdatedAt = field.NewTime(tableName, "updated_at")
	_userStatistic.Country = field.NewString(tableName, "country")
	_userStatistic.RegionID = field.NewString(tableName, "region_id")
	_userStatistic.UserType = field.NewString(tableName, "user_type")
	_userStatistic.TotalCount = field.NewInt32(tableName, "total_count")
	_userStatistic.ActiveCount = field.NewInt32(tableName, "active_count")

	_userStatistic.fillFieldMap()

	return _userStatistic
}

type userStatistic struct {
	userStatisticDo

	ALL         field.Asterisk
	ID          field.String
	CreatedAt   field.Time
	UpdatedAt   field.Time
	Country     field.String
	RegionID    field.String
	UserType    field.String
	TotalCount  field.Int32
	ActiveCount field.Int32

	fieldMap map[string]field.Expr
}

func (u userStatistic) Table(newTableName string) *userStatistic {
	u.userStatisticDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userStatistic) As(alias string) *userStatistic {
	u.userStatisticDo.DO = *(u.userStatisticDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userStatistic) updateTableName(table string) *userStatistic {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewString(table, "id")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")
	u.Country = field.NewString(table, "country")
	u.RegionID = field.NewString(table, "region_id")
	u.UserType = field.NewString(table, "user_type")
	u.TotalCount = field.NewInt32(table, "total_count")
	u.ActiveCount = field.NewInt32(table, "active_count")

	u.fillFieldMap()

	return u
}

func (u *userStatistic) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userStatistic) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 8)
	u.fieldMap["id"] = u.ID
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["country"] = u.Country
	u.fieldMap["region_id"] = u.RegionID
	u.fieldMap["user_type"] = u.UserType
	u.fieldMap["total_count"] = u.TotalCount
	u.fieldMap["active_count"] = u.ActiveCount
}

func (u userStatistic) clone(db *gorm.DB) userStatistic {
	u.userStatisticDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userStatistic) replaceDB(db *gorm.DB) userStatistic {
	u.userStatisticDo.ReplaceDB(db)
	return u
}

type userStatisticDo struct{ gen.DO }

type IUserStatisticDo interface {
	gen.SubQuery
	Debug() IUserStatisticDo
	WithContext(ctx context.Context) IUserStatisticDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserStatisticDo
	WriteDB() IUserStatisticDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserStatisticDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserStatisticDo
	Not(conds ...gen.Condition) IUserStatisticDo
	Or(conds ...gen.Condition) IUserStatisticDo
	Select(conds ...field.Expr) IUserStatisticDo
	Where(conds ...gen.Condition) IUserStatisticDo
	Order(conds ...field.Expr) IUserStatisticDo
	Distinct(cols ...field.Expr) IUserStatisticDo
	Omit(cols ...field.Expr) IUserStatisticDo
	Join(table schema.Tabler, on ...field.Expr) IUserStatisticDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserStatisticDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserStatisticDo
	Group(cols ...field.Expr) IUserStatisticDo
	Having(conds ...gen.Condition) IUserStatisticDo
	Limit(limit int) IUserStatisticDo
	Offset(offset int) IUserStatisticDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserStatisticDo
	Unscoped() IUserStatisticDo
	Create(values ...*entities.UserStatistic) error
	CreateInBatches(values []*entities.UserStatistic, batchSize int) error
	Save(values ...*entities.UserStatistic) error
	First() (*entities.UserStatistic, error)
	Take() (*entities.UserStatistic, error)
	Last() (*entities.UserStatistic, error)
	Find() ([]*entities.UserStatistic, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.UserStatistic, err error)
	FindInBatches(result *[]*entities.UserStatistic, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*entities.UserStatistic) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserStatisticDo
	Assign(attrs ...field.AssignExpr) IUserStatisticDo
	Joins(fields ...field.RelationField) IUserStatisticDo
	Preload(fields ...field.RelationField) IUserStatisticDo
	FirstOrInit() (*entities.UserStatistic, error)
	FirstOrCreate() (*entities.UserStatistic, error)
	FindByPage(offset int, limit int) (result []*entities.UserStatistic, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserStatisticDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userStatisticDo) Debug() IUserStatisticDo {
	return u.withDO(u.DO.Debug())
}

func (u userStatisticDo) WithContext(ctx context.Context) IUserStatisticDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userStatisticDo) ReadDB() IUserStatisticDo {
	return u.Clauses(dbresolver.Read)
}

func (u userStatisticDo) WriteDB() IUserStatisticDo {
	return u.Clauses(dbresolver.Write)
}

func (u userStatisticDo) Session(config *gorm.Session) IUserStatisticDo {
	return u.withDO(u.DO.Session(config))
}

func (u userStatisticDo) Clauses(conds ...clause.Expression) IUserStatisticDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userStatisticDo) Returning(value interface{}, columns ...string) IUserStatisticDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userStatisticDo) Not(conds ...gen.Condition) IUserStatisticDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userStatisticDo) Or(conds ...gen.Condition) IUserStatisticDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userStatisticDo) Select(conds ...field.Expr) IUserStatisticDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userStatisticDo) Where(conds ...gen.Condition) IUserStatisticDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userStatisticDo) Order(conds ...field.Expr) IUserStatisticDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userStatisticDo) Distinct(cols ...field.Expr) IUserStatisticDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userStatisticDo) Omit(cols ...field.Expr) IUserStatisticDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userStatisticDo) Join(table schema.Tabler, on ...field.Expr) IUserStatisticDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userStatisticDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserStatisticDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userStatisticDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserStatisticDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userStatisticDo) Group(cols ...field.Expr) IUserStatisticDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userStatisticDo) Having(conds ...gen.Condition) IUserStatisticDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userStatisticDo) Limit(limit int) IUserStatisticDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userStatisticDo) Offset(offset int) IUserStatisticDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userStatisticDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserStatisticDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userStatisticDo) Unscoped() IUserStatisticDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userStatisticDo) Create(values ...*entities.UserStatistic) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userStatisticDo) CreateInBatches(values []*entities.UserStatistic, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userStatisticDo) Save(values ...*entities.UserStatistic) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userStatisticDo) First() (*entities.UserStatistic, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entities.UserStatistic), nil
	}
}

func (u userStatisticDo) Take() (*entities.UserStatistic, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entities.UserStatistic), nil
	}
}

func (u userStatisticDo) Last() (*entities.UserStatistic, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entities.UserStatistic), nil
	}
}

func (u userStatisticDo) Find() ([]*entities.UserStatistic, error) {
	result, err := u.DO.Find()
	return result.([]*entities.UserStatistic), err
}

func (u userStatisticDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entities.UserStatistic, err error) {
	buf := make([]*entities.UserStatistic, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userStatisticDo) FindInBatches(result *[]*entities.UserStatistic, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userStatisticDo) Attrs(attrs ...field.AssignExpr) IUserStatisticDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userStatisticDo) Assign(attrs ...field.AssignExpr) IUserStatisticDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userStatisticDo) Joins(fields ...field.RelationField) IUserStatisticDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userStatisticDo) Preload(fields ...field.RelationField) IUserStatisticDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userStatisticDo) FirstOrInit() (*entities.UserStatistic, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entities.UserStatistic), nil
	}
}

func (u userStatisticDo) FirstOrCreate() (*entities.UserStatistic, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entities.UserStatistic), nil
	}
}

func (u userStatisticDo) FindByPage(offset int, limit int) (result []*entities.UserStatistic, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userStatisticDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userStatisticDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userStatisticDo) Delete(models ...*entities.UserStatistic) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userStatisticDo) withDO(do gen.Dao) *userStatisticDo {
	u.DO = *do.(*gen.DO)
	return u
}
