package orderstatistics

import (
	"log"
	"net/http"

	"as-api/as/api/admin/order-statistics/handler"
	"as-api/as/api/admin/order-statistics/service"
	"as-api/as/foundations/logger"
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/apiutil"

	"github.com/go-chi/chi/v5"
)

func init() {
	if err := di.RegisterProviders(NewAPI); err != nil {
		log.Fatal("register constructor order-statistics controller failed:", err)
	}
}

// NewAPI creates a new order-statistics API instance
func NewAPI(svc service.OrderStatisticsService, logger logger.Logger) ServerInterface {
	h := handler.NewOrderStatisticsHandler(svc, logger)
	return h
}

// HandlerAPI registers order-statistics routes with the router using generated handler
func HandlerAPI(si ServerInterface, r chi.Router, log logger.Logger) http.Handler {
	// Use the generated HandlerWithOptions for proper route registration
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter:       r,
		ErrorHandlerFunc: apiutil.ErrorHandlerFunc(log),
	})
}
