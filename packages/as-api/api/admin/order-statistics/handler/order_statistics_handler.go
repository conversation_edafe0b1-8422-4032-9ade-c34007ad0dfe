package handler

import (
	"context"
	"net/http"

	dtos "as-api/as/dtos/admin"
	"as-api/as/foundations/logger"
	"as-api/as/pkg/helpers/apiutil"

	"github.com/pkg/errors"
)

// OrderStatisticsService interface defines order statistics service operations
type OrderStatisticsService interface {
	GetOrderStatistics(ctx context.Context, year, month int, countryCode, regionID *string) (*dtos.OrderStatisticsResponse, error)
}

// OrderStatisticsHandler handles HTTP requests for order statistics
type OrderStatisticsHandler struct {
	service OrderStatisticsService
	logger  logger.Logger
}

// NewOrderStatisticsHandler creates a new order statistics handler
func NewOrderStatisticsHandler(service OrderStatisticsService, logger logger.Logger) *OrderStatisticsHandler {
	return &OrderStatisticsHandler{
		service: service,
		logger:  logger,
	}
}

// GetOrderStatistics handles GET /order-statistics requests
func (h *OrderStatisticsHandler) GetOrderStatistics(w http.ResponseWriter, r *http.Request, params dtos.GetOrderStatisticsParams) {
	response := apiutil.NewJSONResponse(w, r, h.logger)

	// Validate required parameters
	if params.Year <= 0 {
		h.logger.Warnf("Invalid year parameter: %d", params.Year)
		response.Failure(apiutil.NewError("INVALID_YEAR", errors.New("Invalid year parameter")))
		return
	}

	if params.Month < 1 || params.Month > 12 {
		h.logger.Warnf("Invalid month parameter: %d", params.Month)
		response.Failure(apiutil.NewError("INVALID_MONTH", errors.New("Invalid month parameter. Must be between 1 and 12")))
		return
	}

	// Validate optional country code format if provided
	if params.CountryCode != nil && len(*params.CountryCode) != 3 {
		h.logger.Warnf("Invalid country code format: %s", *params.CountryCode)
		response.Failure(apiutil.NewError("INVALID_COUNTRY_CODE", errors.New("Invalid country code format. Must be 3 characters")))
		return
	}

	// Call service to get order statistics
	result, err := h.service.GetOrderStatistics(r.Context(), params.Year, params.Month, params.CountryCode, params.RegionId)
	if err != nil {
		response.Failure(err)
		return
	}

	// Return successful response
	if err := response.Success(http.StatusOK, result); err != nil {
		h.logger.Error(err.Error())
	}
}
