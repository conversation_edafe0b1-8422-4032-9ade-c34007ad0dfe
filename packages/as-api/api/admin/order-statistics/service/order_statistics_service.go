package service

import (
	"context"
	"log"

	dtos "as-api/as/dtos/admin"
	"as-api/as/internal/cms"
	orderstatistics "as-api/as/internal/order-statistics"
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/sliceutils"

	"github.com/pkg/errors"
)

func init() {
	if err := di.RegisterProviders(NewOrderStatisticsService); err != nil {
		log.Fatal("register constructor order statistics service failed:", err)
	}
}

// OrderStatisticsService handles business logic for order statistics management
type OrderStatisticsService interface {
	GetOrderStatistics(ctx context.Context, year, month int, countryCode, regionID *string) (*dtos.OrderStatisticsResponse, error)
}

type orderStatisticsService struct {
	orderStatisticsDomain orderstatistics.OrderStatisticsDomain
	cms                   cms.CmsDomain
}

// NewOrderStatisticsService creates a new order statistics service instance
func NewOrderStatisticsService(
	orderStatisticsDomain orderstatistics.OrderStatisticsDomain,
	cms cms.CmsDomain,
) OrderStatisticsService {
	return &orderStatisticsService{
		orderStatisticsDomain: orderStatisticsDomain,
		cms:                   cms,
	}
}

// GetOrderStatistics retrieves order statistics for the specified parameters
func (s *orderStatisticsService) GetOrderStatistics(ctx context.Context, year, month int, countryCode, regionID *string) (*dtos.OrderStatisticsResponse, error) {
	filter := &orderstatistics.OrderStatisticsFilter{
		Year:        year,
		Month:       month,
		CountryCode: countryCode,
		RegionID:    regionID,
	}

	result, err := s.orderStatisticsDomain.GetOrderStatistics(ctx, filter)
	if err != nil {
		return nil, errors.Wrap(err, "get order statistics from domain")
	}

	// Convert domain result to DTO
	dto := s.convertToDTO(result)

	// Enrich with country and region names
	if err := s.enrichWithLocationNames(ctx, dto); err != nil {
		return nil, errors.Wrap(err, "enrich with location names")
	}

	return &dtos.OrderStatisticsResponse{
		Data: *dto,
	}, nil
}

// convertToDTO converts domain result to DTO format
func (s *orderStatisticsService) convertToDTO(result *orderstatistics.OrderStatisticsResult) *dtos.OrderStatistics {
	dto := &dtos.OrderStatistics{
		SellerRevenue: dtos.SellerRevenue{
			OrderCount:        int(result.SellerRevenue.OrderCount),
			TotalOrderAmount:  result.SellerRevenue.TotalOrderAmount,
			TotalSalesAmount:  result.SellerRevenue.TotalSalesAmount,
			TotalProductPrice: result.SellerRevenue.TotalProductPrice,
			SellerTaxes:       make([]dtos.SellerTax, 0, len(result.SellerRevenue.SellerTaxes)),
		},
		MarketplaceVAT: dtos.MarketplaceVAT{
			VatObligations: make([]dtos.VatObligation, 0, len(result.MarketplaceVAT.VatObligations)),
		},
		PlatformFees: dtos.PlatformFees{
			MemberDiscount: result.PlatformFees.MemberDiscount,
			SellingFees:    result.PlatformFees.SellingFees,
			BuyingFees:     result.PlatformFees.BuyingFees,
		},
		CountryBreakdown: make([]dtos.CountryBreakdown, 0, len(result.CountryBreakdown)),
		RegionBreakdown:  make([]dtos.RegionBreakdown, 0, len(result.RegionBreakdown)),
	}

	// Convert seller taxes
	for _, tax := range result.SellerRevenue.SellerTaxes {
		dto.SellerRevenue.SellerTaxes = append(dto.SellerRevenue.SellerTaxes, dtos.SellerTax{
			TaxAmount: tax.TaxAmount,
			TaxRate:   tax.TaxRate,
		})
	}

	// Convert VAT obligations
	for _, vat := range result.MarketplaceVAT.VatObligations {
		dto.MarketplaceVAT.VatObligations = append(dto.MarketplaceVAT.VatObligations, dtos.VatObligation{
			Amount:    vat.Amount,
			TaxAmount: vat.TaxAmount,
			TaxRate:   vat.TaxRate,
		})
	}

	// Convert country breakdowns
	for _, country := range result.CountryBreakdown {
		countryDTO := dtos.CountryBreakdown{
			CountryCode:   country.CountryCode,
			SellerRevenue: s.convertSellerRevenueToDTO(country.SellerRevenue),
			MarketplaceVAT: dtos.MarketplaceVAT{
				VatObligations: make([]dtos.VatObligation, 0, len(country.MarketplaceVAT.VatObligations)),
			},
			PlatformFees: dtos.PlatformFees{
				MemberDiscount: country.PlatformFees.MemberDiscount,
				SellingFees:    country.PlatformFees.SellingFees,
				BuyingFees:     country.PlatformFees.BuyingFees,
			},
		}

		for _, vat := range country.MarketplaceVAT.VatObligations {
			countryDTO.MarketplaceVAT.VatObligations = append(countryDTO.MarketplaceVAT.VatObligations, dtos.VatObligation{
				Amount:    vat.Amount,
				TaxAmount: vat.TaxAmount,
				TaxRate:   vat.TaxRate,
			})
		}

		dto.CountryBreakdown = append(dto.CountryBreakdown, countryDTO)
	}

	// Convert region breakdowns
	for _, region := range result.RegionBreakdown {
		regionDTO := dtos.RegionBreakdown{
			RegionId:      region.RegionID,
			SellerRevenue: s.convertSellerRevenueToDTO(region.SellerRevenue),
			MarketplaceVAT: dtos.MarketplaceVAT{
				VatObligations: make([]dtos.VatObligation, 0, len(region.MarketplaceVAT.VatObligations)),
			},
			PlatformFees: dtos.PlatformFees{
				MemberDiscount: region.PlatformFees.MemberDiscount,
				SellingFees:    region.PlatformFees.SellingFees,
				BuyingFees:     region.PlatformFees.BuyingFees,
			},
		}

		for _, vat := range region.MarketplaceVAT.VatObligations {
			regionDTO.MarketplaceVAT.VatObligations = append(regionDTO.MarketplaceVAT.VatObligations, dtos.VatObligation{
				Amount:    vat.Amount,
				TaxAmount: vat.TaxAmount,
				TaxRate:   vat.TaxRate,
			})
		}

		dto.RegionBreakdown = append(dto.RegionBreakdown, regionDTO)
	}

	return dto
}

// convertSellerRevenueToDTO converts seller revenue data to DTO format
func (s *orderStatisticsService) convertSellerRevenueToDTO(revenue *orderstatistics.SellerRevenueData) dtos.SellerRevenue {
	dto := dtos.SellerRevenue{
		OrderCount:        int(revenue.OrderCount),
		TotalOrderAmount:  revenue.TotalOrderAmount,
		TotalSalesAmount:  revenue.TotalSalesAmount,
		TotalProductPrice: revenue.TotalProductPrice,
		SellerTaxes:       make([]dtos.SellerTax, 0, len(revenue.SellerTaxes)),
	}

	for _, tax := range revenue.SellerTaxes {
		dto.SellerTaxes = append(dto.SellerTaxes, dtos.SellerTax{
			TaxAmount: tax.TaxAmount,
			TaxRate:   tax.TaxRate,
		})
	}

	return dto
}

// enrichWithLocationNames enriches the DTO with actual country and region names
func (s *orderStatisticsService) enrichWithLocationNames(ctx context.Context, dto *dtos.OrderStatistics) error {
	// Collect country codes and region IDs
	countryCodes := sliceutils.Map(dto.CountryBreakdown, func(country dtos.CountryBreakdown) string {
		return country.CountryCode
	})

	regionIds := sliceutils.Map(dto.RegionBreakdown, func(region dtos.RegionBreakdown) string {
		return region.RegionId
	})

	// Get country information
	if len(countryCodes) > 0 {
		countries, err := s.cms.GetCountryByCodes(ctx, countryCodes...)
		if err != nil {
			return errors.Wrap(err, "get countries by codes")
		}

		countryMap := make(map[string]map[string]string)
		for _, country := range countries {
			if country.DisplayName != nil {
				countryMap[country.Code] = country.DisplayName
			}
		}

		// Update country display names in DTO
		for i := range dto.CountryBreakdown {
			if displayName, ok := countryMap[dto.CountryBreakdown[i].CountryCode]; ok {
				dto.CountryBreakdown[i].DisplayName = &displayName
			}
		}
	}

	// Get region information
	if len(regionIds) > 0 {
		regions, err := s.cms.GetRegionsByIds(ctx, regionIds...)
		if err != nil {
			return errors.Wrap(err, "get regions by ids")
		}

		regionMap := make(map[string]map[string]string)
		for _, region := range regions {
			if region.DisplayName != nil {
				regionMap[region.Id] = region.DisplayName
			}
		}

		// Update region display names in DTO
		for i := range dto.RegionBreakdown {
			if displayName, ok := regionMap[dto.RegionBreakdown[i].RegionId]; ok {
				dto.RegionBreakdown[i].DisplayName = &displayName
			}
		}
	}

	return nil
}
