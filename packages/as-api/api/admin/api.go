package admin

import (
	"as-api/as/api/admin/auth"
	"as-api/as/api/admin/banner"
	"as-api/as/api/admin/cms"
	"as-api/as/api/admin/country"
	"as-api/as/api/admin/inquiry"
	"as-api/as/api/admin/master"
	"as-api/as/api/admin/order"
	orderstatistics "as-api/as/api/admin/order-statistics"
	"as-api/as/api/admin/product"
	"as-api/as/api/admin/report"
	"as-api/as/api/admin/seller"
	"as-api/as/api/admin/system"
	"as-api/as/api/admin/upload"
	"as-api/as/api/admin/user"
	userstatistics "as-api/as/api/admin/user-statistics"
	"as-api/as/foundations/logger"

	"github.com/go-chi/chi/v5"
	"go.uber.org/dig"
)

type HTTPHandlerAdmin struct {
	dig.In

	Logger          logger.Logger
	Auth            auth.ServerInterface
	CMS             cms.ServerInterface
	User            user.ServerInterface
	Master          master.ServerInterface
	Banner          banner.ServerInterface
	Seller          seller.ServerInterface
	Product         product.ServerInterface
	Upload          upload.ServerInterface
	Report          report.ServerInterface
	Inquiry         inquiry.ServerInterface
	Order           order.ServerInterface
	OrderStatistics orderstatistics.ServerInterface
	Country         country.ServerInterface
	System          system.ServerInterface
	UserStatistics  userstatistics.ServerInterface
}

func (h *HTTPHandlerAdmin) HandlerAPI(r chi.Router) {
	auth.HandlerAPI(h.Auth, r, h.Logger)
	cms.HandlerAPI(h.CMS, r, h.Logger)
	user.HandlerAPI(h.User, r, h.Logger)
	master.HandlerAPI(h.Master, r, h.Logger)
	banner.HandlerAPI(h.Banner, r, h.Logger)
	seller.HandlerAPI(h.Seller, r, h.Logger)
	product.HandlerAPI(h.Product, r, h.Logger)
	upload.HandlerAPI(h.Upload, r, h.Logger)
	report.HandlerAPI(h.Report, r, h.Logger)
	inquiry.HandlerAPI(h.Inquiry, r, h.Logger)
	order.HandlerAPI(h.Order, r, h.Logger)
	orderstatistics.HandlerAPI(h.OrderStatistics, r, h.Logger)
	country.HandlerAPI(h.Country, r, h.Logger)
	system.HandlerAPI(h.System, r, h.Logger)
	userstatistics.HandlerAPI(h.UserStatistics, r, h.Logger)
}
