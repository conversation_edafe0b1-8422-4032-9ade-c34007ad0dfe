package handler

import (
	"context"
	"net/http"

	dtos "as-api/as/dtos/admin"
	"as-api/as/foundations/logger"
	"as-api/as/pkg/helpers/apiutil"

	"github.com/pkg/errors"
)

// UserStatisticsService interface defines user statistics service operations
type UserStatisticsService interface {
	GetGlobalUserSummary(ctx context.Context) (*dtos.GlobalUserSummaryResponse, error)
	GetCountryUserSummary(ctx context.Context, countryCode string) (*dtos.CountryUserSummaryResponse, error)
	GetRegionUserSummary(ctx context.Context, regionId string) (*dtos.RegionUserSummaryResponse, error)
}

// userStatisticsHandler handles user-statistics-related HTTP requests
// This implements the generated ServerInterface from user_statistics.gen.go
type userStatisticsHandler struct {
	service UserStatisticsService
	logger  logger.Logger
}

// NewUserStatisticsHandler creates a new user-statistics handler
func NewUserStatisticsHandler(service UserStatisticsService, logger logger.Logger) *userStatisticsHandler {
	return &userStatisticsHandler{
		service: service,
		logger:  logger,
	}
}

// GetGlobalUserSummary handles GET /admin/v1/user-statistics/global-summary
// Implements ServerInterface.GetGlobalUserSummary
func (h *userStatisticsHandler) GetGlobalUserSummary(w http.ResponseWriter, r *http.Request) {
	response := apiutil.NewJSONResponse(w, r, h.logger)

	summary, err := h.service.GetGlobalUserSummary(r.Context())
	if err != nil {
		response.Failure(err)
		return
	}

	if err := response.Success(http.StatusOK, summary); err != nil {
		h.logger.Error(err.Error())
	}
}

// GetCountryUserSummary handles GET /admin/v1/user-statistics/country-summary
// Implements ServerInterface.GetCountryUserSummary
func (h *userStatisticsHandler) GetCountryUserSummary(w http.ResponseWriter, r *http.Request, params dtos.GetCountryUserSummaryParams) {
	response := apiutil.NewJSONResponse(w, r, h.logger)

	if params.CountryCode == "" {
		response.Failure(errors.New("countryCode parameter is required"))
		return
	}

	summary, err := h.service.GetCountryUserSummary(r.Context(), params.CountryCode)
	if err != nil {
		response.Failure(err)
		return
	}

	if err := response.Success(http.StatusOK, summary); err != nil {
		h.logger.Error(err.Error())
	}
}

// GetRegionUserSummary handles GET /admin/v1/user-statistics/region-summary
// Implements ServerInterface.GetRegionUserSummary
func (h *userStatisticsHandler) GetRegionUserSummary(w http.ResponseWriter, r *http.Request, params dtos.GetRegionUserSummaryParams) {
	response := apiutil.NewJSONResponse(w, r, h.logger)

	if params.RegionId == "" {
		response.Failure(errors.New("regionId parameter is required"))
		return
	}

	summary, err := h.service.GetRegionUserSummary(r.Context(), params.RegionId)
	if err != nil {
		response.Failure(err)
		return
	}

	if err := response.Success(http.StatusOK, summary); err != nil {
		h.logger.Error(err.Error())
	}
}
