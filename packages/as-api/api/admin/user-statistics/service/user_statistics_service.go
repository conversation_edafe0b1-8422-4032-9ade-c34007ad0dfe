package service

import (
	"context"
	"log"

	dtos "as-api/as/dtos/admin"
	"as-api/as/internal/cms"
	userstatistics "as-api/as/internal/user-statistics"
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/sliceutils"

	"github.com/pkg/errors"
)

func init() {
	if err := di.RegisterProviders(NewUserStatisticsService); err != nil {
		log.Fatal("register constructor user-statistics service failed:", err)
	}
}

// UserStatisticsService handles business logic for user statistics management
type UserStatisticsService interface {
	GetGlobalUserSummary(ctx context.Context) (*dtos.GlobalUserSummaryResponse, error)
	GetCountryUserSummary(ctx context.Context, countryCode string) (*dtos.CountryUserSummaryResponse, error)
	GetRegionUserSummary(ctx context.Context, regionId string) (*dtos.RegionUserSummaryResponse, error)
}

type userStatisticsService struct {
	userStatisticsDomain userstatistics.UserStatisticsDomain
	cms                  cms.CmsDomain
}

// NewUserStatisticsService creates a new user-statistics service instance
func NewUserStatisticsService(userStatisticsDomain userstatistics.UserStatisticsDomain, cms cms.CmsDomain) UserStatisticsService {
	return &userStatisticsService{
		userStatisticsDomain: userStatisticsDomain,
		cms:                  cms,
	}
}

// GetGlobalUserSummary retrieves global user statistics summary
func (s *userStatisticsService) GetGlobalUserSummary(ctx context.Context) (*dtos.GlobalUserSummaryResponse, error) {
	filter := &userstatistics.UserStatisticsFilter{}

	summary, err := s.userStatisticsDomain.GetStatisticsSummary(ctx, filter)
	if err != nil {
		return nil, errors.Wrap(err, "get global user summary from domain")
	}

	countryCodes := sliceutils.Map(summary.CountryBreakdown, func(country *userstatistics.CountryStatistics) string {
		return country.Country
	})

	countries, err := s.cms.GetCountryByCodes(ctx, countryCodes...)
	if err != nil {
		return nil, errors.Wrap(err, "get countries by codes")
	}

	mCountries := sliceutils.MapToMap(countries, func(country *cms.Country) (string, *cms.Country) {
		return country.Code, country
	})

	response := &dtos.GlobalUserSummaryResponse{
		Summary: mapDomainToDTO(summary, mCountries, nil),
	}

	return response, nil
}

// GetCountryUserSummary retrieves user statistics summary for a specific country
func (s *userStatisticsService) GetCountryUserSummary(ctx context.Context, countryCode string) (*dtos.CountryUserSummaryResponse, error) {
	filter := &userstatistics.UserStatisticsFilter{
		Country: &countryCode,
	}

	summary, err := s.userStatisticsDomain.GetStatisticsSummary(ctx, filter)
	if err != nil {
		return nil, errors.Wrap(err, "get country user summary from domain")
	}

	regionIds := sliceutils.Map(summary.RegionBreakdown, func(region *userstatistics.RegionStatistics) string {
		return region.RegionID
	})

	regions, err := s.cms.GetRegionsByIds(ctx, regionIds...)
	if err != nil {
		return nil, errors.Wrap(err, "get regions by codes")
	}

	mRegions := sliceutils.MapToMap(regions, func(region *cms.Region) (string, *cms.Region) {
		return region.Id, region
	})

	dtoSummary := mapDomainToDTO(summary, nil, mRegions)
	countries, err := s.cms.GetCountryByCodes(ctx, countryCode)
	if err != nil {
		return nil, errors.Wrap(err, "get countries by codes")
	}
	if len(countries) > 0 {
		dtoSummary.DisplayName = &countries[0].DisplayName
	}

	response := &dtos.CountryUserSummaryResponse{
		Summary: dtoSummary,
	}

	return response, nil
}

// GetRegionUserSummary retrieves user statistics summary for a specific region
func (s *userStatisticsService) GetRegionUserSummary(ctx context.Context, regionId string) (*dtos.RegionUserSummaryResponse, error) {
	filter := &userstatistics.UserStatisticsFilter{
		RegionID: &regionId,
	}

	summary, err := s.userStatisticsDomain.GetStatisticsSummary(ctx, filter)
	if err != nil {
		return nil, errors.Wrap(err, "get region user summary from domain")
	}

	regionIds := sliceutils.Map(summary.RegionBreakdown, func(region *userstatistics.RegionStatistics) string {
		return region.RegionID
	})

	regions, err := s.cms.GetRegionsByIds(ctx, append(regionIds, regionId)...)
	if err != nil {
		return nil, errors.Wrap(err, "get regions by codes")
	}

	mRegions := sliceutils.MapToMap(regions, func(region *cms.Region) (string, *cms.Region) {
		return region.Id, region
	})

	dtoSummary := mapDomainToDTO(summary, nil, mRegions)
	if mRegions[regionId] != nil {
		dtoSummary.DisplayName = &mRegions[regionId].DisplayName
	}

	response := &dtos.RegionUserSummaryResponse{
		Summary: dtoSummary,
	}

	return response, nil
}

// mapDomainToDTO maps domain StatisticsSummary to DTO UserStatistics
func mapDomainToDTO(domain *userstatistics.StatisticsSummary, mCountries map[string]*cms.Country, mRegions map[string]*cms.Region) dtos.UserStatistics {
	dto := dtos.UserStatistics{
		TotalUsers:   domain.TotalUsers,
		ActiveUsers:  domain.ActiveUsers,
		SellerUsers:  domain.SellerUsers,
		PremiumUsers: domain.PremiumUsers,
		GeneralUsers: domain.GeneralUsers,
	}

	// Map country breakdown if present
	if domain.CountryBreakdown != nil {
		countryBreakdown := make([]dtos.CountryStatistics, len(domain.CountryBreakdown))
		for i, country := range domain.CountryBreakdown {
			var displayName *map[string]string
			if c := mCountries[country.Country]; c != nil {
				displayName = &c.DisplayName
			}

			countryBreakdown[i] = dtos.CountryStatistics{
				Country:      country.Country,
				DisplayName:  displayName,
				TotalUsers:   country.TotalUsers,
				ActiveUsers:  country.ActiveUsers,
				SellerUsers:  country.SellerUsers,
				PremiumUsers: country.PremiumUsers,
				GeneralUsers: country.GeneralUsers,
			}
		}
		dto.CountryBreakdown = &countryBreakdown
	}

	// Map region breakdown if present
	if domain.RegionBreakdown != nil {
		regionBreakdown := make([]dtos.RegionStatistics, len(domain.RegionBreakdown))
		for i, region := range domain.RegionBreakdown {
			var displayName *map[string]string
			if c := mRegions[region.RegionID]; c != nil {
				displayName = &c.DisplayName
			}

			regionBreakdown[i] = dtos.RegionStatistics{
				RegionId:     region.RegionID,
				DisplayName:  displayName,
				TotalUsers:   region.TotalUsers,
				ActiveUsers:  region.ActiveUsers,
				SellerUsers:  region.SellerUsers,
				PremiumUsers: region.PremiumUsers,
				GeneralUsers: region.GeneralUsers,
			}
		}
		dto.RegionBreakdown = &regionBreakdown
	}

	return dto
}
