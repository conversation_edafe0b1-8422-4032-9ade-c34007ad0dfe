package userstatistics

import (
	"log"
	"net/http"

	"as-api/as/api/admin/user-statistics/handler"
	"as-api/as/api/admin/user-statistics/service"
	"as-api/as/foundations/logger"
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/apiutil"

	"github.com/go-chi/chi/v5"
)

func init() {
	if err := di.RegisterProviders(NewAPI); err != nil {
		log.Fatal("register constructor user-statistics controller failed:", err)
	}
}

// NewAPI creates a new user-statistics API instance
func NewAPI(svc service.UserStatisticsService, logger logger.Logger) ServerInterface {
	h := handler.NewUserStatisticsHandler(svc, logger)
	return h
}

// HandlerAPI registers user-statistics routes with the router using generated handler
func HandlerAPI(si ServerInterface, r chi.Router, log logger.Logger) http.Handler {
	// Use the generated HandlerWithOptions for proper route registration
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter:       r,
		ErrorHandlerFunc: apiutil.ErrorHandlerFunc(log),
	})
}
