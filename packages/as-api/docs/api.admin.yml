openapi: 3.1.0
info:
  title: AS API Admin
  version: '1.0'
  description: This is API specification for AS project.
servers:
  - url: 'http://localhost:3000/admin/v1'
paths:
  /auth:
    post:
      operationId: auth
      summary: POST auth
      description: Auth request
      tags:
        - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /system/maintenance-status:
    get:
      summary: Get maintenance status
      description: Retrieve the current system maintenance status
      operationId: get-maintenance-status
      tags:
        - system
      responses:
        '200':
          description: Successfully retrieved maintenance status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceStatus'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      summary: Update maintenance status
      description: Enable or disable system maintenance mode
      operationId: update-maintenance-status
      tags:
        - system
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MaintenanceUpdateRequest'
      responses:
        '200':
          description: Successfully updated maintenance status
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MaintenanceStatus'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /system/postal-codes:
    get:
      operationId: get-postal-codes
      summary: Get US postal codes
      description: Get a list of all valid US postal codes (ZIP codes) for admin management
      tags:
        - system
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostalCodesResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Admin access required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /cms/content-categories:
    get:
      operationId: get-content-categories
      summary: Get all content categories
      description: Get all content categories
      tags:
        - cms
      security:
        - bearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ContentCategory'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /cms/contents:
    get:
      operationId: get-contents
      summary: Get all contents
      description: Get all contents
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
        - name: contentCategoryCode
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Content'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-content
      summary: Create a new content
      description: Create a new content
      tags:
        - cms
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Content'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Content'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/cms/contents/{id}':
    get:
      operationId: get-content-by-id
      summary: Get content by id
      description: Get content by id
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Content'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-content
      summary: Update a content
      description: Update a content
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Content'
      responses:
        '204':
          description: No Content
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-content
      summary: Delete a content
      description: Delete a content
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No Content
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /cms/languages:
    get:
      operationId: get-languages
      summary: Get all languages
      description: Get all languages
      tags:
        - cms
      security:
        - bearerAuth: []
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Language'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /cms/notifications:
    get:
      operationId: get-notifications
      summary: Get notifications
      description: Get notifications
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetNotificationsResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-notification
      summary: Create notification
      description: Create notification
      tags:
        - cms
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Notification'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/cms/notifications/{id}':
    get:
      operationId: get-notification-by-id
      summary: Get notification by id
      description: Get notification by id
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-notification
      summary: Update notification
      description: Update notification
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Notification'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-notification
      summary: Delete notification
      description: Delete notification
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /cms/faq-categories-branches:
    get:
      operationId: get-faq-categories-branches
      summary: Get faq categories branches
      description: Get faq categories branches
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FaqCategoriesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-faq-categories-branch
      summary: Create faq categories branch
      description: Create faq categories branch
      tags:
        - cms
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FaqCategoriesBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaqCategoriesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/cms/faq-categories-branches/{id}':
    get:
      operationId: get-faq-categories-branch-by-id
      summary: Get faq categories branch by id
      description: Get faq categories branch by id
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FaqCategoriesBranch'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /cms/terms-branches:
    get:
      operationId: get-terms-branches
      summary: Get terms branches
      description: Get terms branches
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
        - name: type
          in: query
          required: true
          schema:
            $ref: '#/components/schemas/TermsType'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TermsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-terms-branch
      summary: Create terms branch
      description: Create terms branch
      tags:
        - cms
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TermsBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TermsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/cms/terms-branches/{id}':
    get:
      operationId: get-terms-branch-by-id
      summary: Get terms branch by id
      description: Get terms branch by id
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TermsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-terms-branch
      summary: Update terms branch
      description: Update terms branch
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TermsBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TermsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-terms-branch
      summary: Delete terms branch
      description: Delete terms branch
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/cms/terms-branches/{id}/publish':
    post:
      operationId: publish-terms-branch
      summary: Publish terms branch
      description: Publish terms branch
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishTermsBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-terms-branch
      summary: Unpublish terms branch
      description: Unpublish terms branch
      tags:
        - cms
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /users:
    get:
      operationId: search-users
      summary: Search Users
      description: Search Users
      tags:
        - user
      security:
        - bearerAuth: []
      parameters:
        - name: accountId
          in: query
          schema:
            type: string
          description: The unique identifier for the user account.
        - name: nickname
          in: query
          schema:
            type: string
          description: The user's display name or alias.
        - name: email
          in: query
          schema:
            type: string
          description: The user's email address.
        - name: accountType
          in: query
          schema:
            $ref: '#/components/schemas/AccountType'
          description: The type of user account.
        - name: status
          in: query
          schema:
            $ref: '#/components/schemas/AccountStatus'
          description: The current status of the user account.
        - name: countryCode
          in: query
          schema:
            type: string
          description: The country of the user account.
        - name: regionId
          in: query
          schema:
            type: string
          description: The current region of the user account.
        - name: postalCode
          in: query
          schema:
            type: string
          description: The postal code of the user account.
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchUsersResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}':
    get:
      operationId: get-user-by-id
      summary: Get a user by id
      description: Get a user by id
      tags:
        - user
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserByIdResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-user-by-id
      summary: Update user by ID
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the user to update.
          schema:
            type: string
      requestBody:
        required: true
        description: The updated user information.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
      responses:
        '204':
          description: Successfully Update User
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/status':
    put:
      operationId: update-user-status
      summary: Update user status by admin
      description: 'Update the status of a user account (active, suspended, restricted, etc.)'
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the user to update status.
          schema:
            type: string
      requestBody:
        required: true
        description: The status update information.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserStatusUpdateRequest'
      responses:
        '204':
          description: Successfully Updated User Status
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /user-statistics/global-summary:
    get:
      summary: Get global user summary
      description: Retrieve overall user statistics across all countries with country breakdown
      operationId: get-global-user-summary
      tags:
        - user-statistics
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GlobalUserSummaryResponse'
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /user-statistics/country-summary:
    get:
      summary: Get country user summary
      description: Retrieve user statistics for a specific country with region breakdown
      operationId: get-country-user-summary
      tags:
        - user-statistics
      security:
        - bearerAuth: []
      parameters:
        - name: countryCode
          in: query
          required: true
          description: 'ISO3 country code (e.g., "USA", "JPN")'
          schema:
            type: string
            example: USA
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CountryUserSummaryResponse'
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Country Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /user-statistics/region-summary:
    get:
      summary: Get region user summary
      description: Retrieve user statistics for a specific region with sub-region breakdown
      operationId: get-region-user-summary
      tags:
        - user-statistics
      security:
        - bearerAuth: []
      parameters:
        - name: regionId
          in: query
          required: true
          description: Unique identifier for the region
          schema:
            type: string
            example: region_123
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegionUserSummaryResponse'
        '400':
          description: Bad Request (Validation Error)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Region Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/product-categories-branches:
    get:
      operationId: get-product-categories-branches
      summary: Get product categories branches
      description: Get product categories branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: status
          in: query
          schema:
            $ref: '#/components/schemas/MasterStatus'
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProductCategoriesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-product-categories-branch
      summary: Create product categories branch
      description: Create product categories branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductCategoriesBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductCategoriesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/product-categories-branches/{id}':
    get:
      operationId: get-product-categories-branch-by-id
      summary: Get product categories branch by id
      description: Get product categories branch by id
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductCategoriesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-product-categories-branch
      summary: Update product categories branch
      description: Update product categories branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductCategoriesBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductCategoriesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-product-categories-branch
      summary: Delete product categories branch
      description: Delete product categories branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/product-categories-branches/{id}/publish':
    post:
      operationId: publish-product-categories-branch
      summary: Publish product categories branch
      description: Publish product categories branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishProductCategoriesBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-product-categories-branch
      summary: Unpublish product categories branch
      description: Unpublish product categories branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/brands-branches:
    get:
      operationId: get-brands-branches
      summary: Get brands branches
      description: Get brands branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BrandsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-brands-branch
      summary: Create brands branch
      description: Create brands branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BrandsBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BrandsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/brands-branches/{id}':
    get:
      operationId: get-brands-branch-by-id
      summary: Get brands branch by ID
      description: Get a specific brands branch by its ID with product counts
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BrandsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-brands-branch
      summary: Update brands branch
      description: Update brands branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BrandsBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BrandsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-brands-branch
      summary: Delete brands branch
      description: Delete brands branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/brands-branches/{id}/publish':
    post:
      operationId: publish-brands-branch
      summary: Publish brands branch
      description: Publish brands branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishBrandsBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-brands-branch
      summary: Unpublish brands branch
      description: Unpublish brands branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/languages-branches:
    get:
      operationId: get-languages-branches
      summary: Get languages branches
      description: Get languages branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/LanguagesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-languages-branch
      summary: Create languages branch
      description: Create languages branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LanguagesBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LanguagesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/languages-branches/{id}':
    put:
      operationId: update-languages-branch
      summary: Update languages branch
      description: Update languages branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LanguagesBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LanguagesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-languages-branch
      summary: Delete languages branch
      description: Delete languages branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/languages-branches/{id}/publish':
    post:
      operationId: publish-languages-branch
      summary: Publish languages branch
      description: Publish languages branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishLanguagesBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-languages-branch
      summary: Unpublish languages branch
      description: Unpublish languages branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /banners:
    get:
      operationId: search-banners
      summary: Search Banners
      description: Search Banners
      tags:
        - banner
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchBannersResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-banner
      summary: POST create banner
      description: Create banner request
      tags:
        - banner
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Banner'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Banner'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/banners/{id}':
    get:
      operationId: get-banner
      summary: Get a banner
      description: Get a banner by id
      tags:
        - banner
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Banner'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
      operationId: update-banner
      summary: Update a banner
      description: Update a banner by id
      tags:
        - banner
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Banner'
      responses:
        '204':
          description: No Content
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-banner
      summary: Delete a banner
      description: Delete a banner by id
      tags:
        - banner
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No Content
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/products/{id}':
    put:
      operationId: update-product-by-id
      summary: Update product by ID
      description: Update an existing product's details using its unique identifier.
      tags:
        - product
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the product to update.
          schema:
            type: string
      requestBody:
        required: true
        description: The updated product information.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductUpdateRequest'
      responses:
        '204':
          description: Successfully Update Product
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      operationId: get-product-by-id
      summary: Get product by ID
      description: Get product by ID
      tags:
        - product
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the product to update.
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/products/{id}/ban':
    put:
      operationId: ban-product-by-id
      summary: Ban product by ID
      tags:
        - product
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the product to update.
          schema:
            type: string
      requestBody:
        required: true
        description: The updated product information.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductBanRequest'
      responses:
        '204':
          description: Successfully Update Product
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /products/search:
    post:
      operationId: search-products
      summary: POST search products
      description: Search products
      tags:
        - product
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchProductsRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchProductsResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/sellers/{id}':
    get:
      operationId: get-seller-profile-by-id
      summary: Get seller profile by ID
      description: 'Retrieve complete seller profile information including company info, KYC, address, and bank account details for admin management.'
      tags:
        - seller
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the seller to retrieve.
          schema:
            type: string
      responses:
        '200':
          description: Successfully retrieved seller profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSellerProfileResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Admin access required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Seller not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/sellers/{id}/products':
    get:
      operationId: get-seller-products
      summary: GET seller's products
      description: Get product list
      security:
        - bearerAuth: []
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: Seller ID
          schema:
            type: string
        - name: saleStatus
          in: query
          schema:
            type: array
            items:
              $ref: '#/components/schemas/ProductSalesStatus'
          description: Filter products by sale status.  Multiple statuses can be provided.
        - name: orderBy
          in: query
          schema:
            $ref: '#/components/schemas/OrderBy'
        - name: sortBy
          in: query
          schema:
            type: string
            enum:
              - createdAt
              - price
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SellerProductsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/ui-words-branches:
    get:
      operationId: get-ui-words-branches
      summary: Get ui words branches
      description: Get ui words branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UiWordsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-ui-words-branch
      summary: Create ui words branch
      description: Create ui words branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UiWordsBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UiWordsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/ui-words-branches/{id}':
    put:
      operationId: update-ui-words-branch
      summary: Update ui words branch
      description: Update ui words branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UiWordsBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UiWordsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-ui-words-branch
      summary: Delete ui words branch
      description: Delete ui words branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/ui-words-branches/{id}/publish':
    post:
      operationId: publish-ui-words-branch
      summary: Publish ui words branch
      description: Publish ui words branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishUiWordsBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-ui-words-branch
      summary: Unpublish ui words branch
      description: Unpublish ui words branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /uploads:
    put:
      operationId: upload-file
      summary: Upload file to S3
      description: This API allows uploading a file to Amazon S3 and returns the key of the stored file.
      tags:
        - upload
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UploadFileRequest'
      responses:
        '200':
          description: File uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadFileResponse'
        '400':
          description: Bad request (missing file or invalid format)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error during file upload
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /emails:
    get:
      operationId: get-list-emails
      summary: Get List Emails
      tags:
        - user
      security:
        - bearerAuth: []
      parameters:
        - name: notificationSettings
          in: query
          schema:
            $ref: '#/components/schemas/NotificationSettingsType'
        - name: regionIds
          in: query
          schema:
            type: array
            items:
              type: string
              format: uuid
        - name: countryCodes
          in: query
          schema:
            type: array
            items:
              type: string
        - name: languages
          in: query
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetListEmailResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/custom-ng-words-branches:
    get:
      operationId: get-custom-ng-words-branches
      summary: Get custom ng words branches
      description: Get custom ng words branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CustomNGWordsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-custom-ng-words-branch
      summary: Create custom ng words branch
      description: Create custom ng words branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomNGWordsBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomNGWordsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/custom-ng-words-branches/{id}':
    put:
      operationId: update-custom-ng-words-branch
      summary: Update custom ng words branch
      description: Update custom ng words branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomNGWordsBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomNGWordsBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-custom-ng-words-branch
      summary: Delete custom ng words branch
      description: Delete custom ng words branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/custom-ng-words-branches/{id}/publish':
    post:
      operationId: publish-custom-ng-words-branch
      summary: Publish custom ng words branch
      description: Publish custom ng words branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishCustomNGWordsBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-custom-ng-words-branch
      summary: Unpublish custom ng words branch
      description: Unpublish custom ng words branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /reports:
    get:
      operationId: get-reports
      summary: Get Reports
      description: Get Reports
      tags:
        - report
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
        - name: orderBy
          in: query
          schema:
            $ref: '#/components/schemas/OrderBy'
        - name: sortBy
          in: query
          schema:
            type: string
            enum:
              - createdAt
              - status
        - name: status
          in: query
          schema:
            $ref: '#/components/schemas/ReportStatus'
        - name: itemId
          in: query
          schema:
            type: string
        - name: itemType
          in: query
          schema:
            $ref: '#/components/schemas/ReportItemType'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetReportsResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/reports/{id}/status':
    put:
      operationId: update-report-status
      summary: Update status of a report
      description: Update status of a report by id
      tags:
        - report
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  $ref: '#/components/schemas/ReportStatus'
      responses:
        '204':
          description: No Content
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/subscription-fees-branches:
    get:
      operationId: get-subscription-fees-branches
      summary: Get subscription fees branches
      description: Get subscription fees branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SubscriptionFeesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-subscription-fees-branch
      summary: Create subscription fees branch
      description: Create subscription fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionFeesBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionFeesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/subscription-fees-branches/{id}':
    put:
      operationId: update-subscription-fees-branch
      summary: Update subscription fees branch
      description: Update subscription fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionFeesBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionFeesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-subscription-fees-branch
      summary: Delete subscription fees branch
      description: Delete subscription fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/subscription-fees-branches/{id}/publish':
    post:
      operationId: publish-subscription-fees-branch
      summary: Publish subscription fees branch
      description: Publish subscription fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishSubscriptionFeesBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-subscription-fees-branch
      summary: Unpublish subscription fees branch
      description: Unpublish subscription fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/commission-sales-branches:
    get:
      operationId: get-commission-sales-branches
      summary: Get commission sales branches
      description: Get commission sales branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CommissionSalesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-commission-sales-branch
      summary: Create commission sales branch
      description: Create commission sales branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommissionSalesBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommissionSalesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/commission-sales-branches/{id}':
    put:
      operationId: update-commission-sales-branch
      summary: Update commission sales branch
      description: Update commission sales branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommissionSalesBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommissionSalesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-commission-sales-branch
      summary: Delete commission sales branch
      description: Delete commission sales branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/commission-sales-branches/{id}/publish':
    post:
      operationId: publish-commission-sales-branch
      summary: Publish commission sales branch
      description: Publish commission sales branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishCommissionSalesBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-commission-sales-branch
      summary: Unpublish commission sales branch
      description: Unpublish commission sales branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/transfer-fees-branches:
    get:
      operationId: get-transfer-fees-branches
      summary: Get transfer fees branches
      description: Get transfer fees branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TransferFeesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-transfer-fees-branch
      summary: Create transfer fees branch
      description: Create transfer fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransferFeesBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransferFeesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/transfer-fees-branches/{id}':
    put:
      operationId: update-transfer-fees-branch
      summary: Update transfer fees branch
      description: Update transfer fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransferFeesBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LanguagesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-transfer-fees-branch
      summary: Delete transfer fees branch
      description: Delete transfer fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/transfer-fees-branches/{id}/publish':
    post:
      operationId: publish-transfer-fees-branch
      summary: Publish transfer fees branch
      description: Publish transfer fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishTransferFeesBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-transfer-fees-branch
      summary: Unpublish transfer fees branch
      description: Unpublish transfer fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /inquiries/stats/area-service:
    get:
      operationId: get-inquiry-stats-area-service
      summary: Get Inquiry Stats Area Service
      description: Get Inquiry Stats Area Service
      tags:
        - inquiry
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetInquiryStatsAreaServiceResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/purchased-items':
    get:
      operationId: get-purchased-items
      summary: Get Purchased Items
      tags:
        - user
      parameters:
        - name: id
          in: path
          required: true
          description: User ID
          schema:
            type: string
        - name: from
          in: query
          schema:
            type: string
            format: date-time
            example: '2025-03-13T14:00:00Z'
        - name: to
          in: query
          schema:
            type: string
            format: date-time
            example: '2025-03-13T14:00:00Z'
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOrderItemResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/sellers/{id}/sold-items':
    get:
      operationId: get-sold-items
      summary: Get Sold Items
      tags:
        - seller
      parameters:
        - name: id
          in: path
          required: true
          description: User ID
          schema:
            type: string
        - name: userId
          in: query
          description: Filter sold items by specific user ID
          schema:
            type: string
        - name: transactionStatuses
          in: query
          description: Filter sold items by transaction status values (multiple values supported)
          schema:
            type: array
            items:
              type: string
        - name: from
          in: query
          schema:
            type: string
            format: date-time
            example: '2025-03-13T14:00:00Z'
        - name: to
          in: query
          schema:
            type: string
            format: date-time
            example: '2025-03-13T14:00:00Z'
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOrderItemResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/purchase-fees-branches:
    get:
      operationId: get-purchase-fees-branches
      summary: Get purchase fees branches
      description: Get purchase fees branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PurchaseFeesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-purchase-fees-branch
      summary: Create purchase fees branch
      description: Create purchase fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseFeesBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseFeesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/purchase-fees-branches/{id}':
    put:
      operationId: update-purchase-fees-branch
      summary: Update purchase fees branch
      description: Update purchase fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseFeesBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LanguagesBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-purchase-fees-branch
      summary: Delete purchase fees branch
      description: Delete purchase fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/purchase-fees-branches/{id}/publish':
    post:
      operationId: publish-purchase-fees-branch
      summary: Publish purchase fees branch
      description: Publish purchase fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishPurchaseFeesBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-purchase-fees-branch
      summary: Unpublish purchase fees branch
      description: Unpublish purchase fees branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /master/country-branches:
    get:
      operationId: get-country-branches
      summary: Get country branches
      description: Get country branches
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: countryCode
          in: query
          schema:
            type: string
            description: Country code
            example: US
        - name: page
          in: query
          schema:
            type: integer
        - name: limit
          in: query
          schema:
            type: integer
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CountryBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    post:
      operationId: create-country-branch
      summary: Create country branch
      description: Create country branch
      tags:
        - master
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CountryBranch'
      responses:
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CountryBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/country-branches/{id}':
    put:
      operationId: update-country-branch
      summary: Update country branch
      description: Update country branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CountryBranch'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CountryBranch'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: delete-country-branch
      summary: Delete country branch
      description: Delete country branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/master/country-branches/{id}/publish':
    post:
      operationId: publish-country-branch
      summary: Publish country branch
      description: Publish country branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PublishCountryBranchRequest'
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      operationId: unpublish-country-branch
      summary: Unpublish country branch
      description: Unpublish country branch
      tags:
        - master
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No content
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '412':
          description: Precondition failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /orders:
    get:
      operationId: get-orders
      summary: Get Orders
      tags:
        - order
      parameters:
        - name: userId
          in: query
          description: User ID
          schema:
            type: string
        - name: from
          in: query
          schema:
            type: string
            format: date-time
            example: '2025-03-13T14:00:00Z'
        - name: to
          in: query
          schema:
            type: string
            format: date-time
            example: '2025-03-13T14:00:00Z'
        - name: page
          in: query
          schema:
            type: integer
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOrderResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/orders/{id}':
    get:
      operationId: get-order-by-id
      summary: Get order by ID
      description: Get order by ID
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the order.
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/orders/{id}/ban':
    post:
      operationId: ban-order-by-id
      summary: Ban order by ID
      description: Ban order by ID
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the order.
          schema:
            type: string
      requestBody:
        required: true
        description: Ban Order body.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BanOrderRequest'
      responses:
        '204':
          description: Successfully Ban Order
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/orders/{id}/cancel':
    post:
      operationId: cancel-order
      summary: Cancel Order (Admin)
      description: |
        Cancel an entire order before it is ready to ship.

        **Requirements:**
        - Order must not have any items with status 'preparingForShipment', 'shipped', 'received', or 'completed'
        - Admin can cancel any order
        - All order details will be canceled and products will be returned to available status

        **What happens when canceled:**
        - All order details status changes to 'canceled'
        - Products are returned to 'available' status
        - Payment is refunded (if already paid)
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the order to cancel
          schema:
            type: string
      responses:
        '204':
          description: Order canceled successfully
        '400':
          description: Bad Request (order cannot be canceled)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden (admin access required)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Order not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict (order cannot be canceled due to status)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/order-items/{id}':
    get:
      operationId: get-order-item-by-id
      summary: Get order item by ID
      description: Get order item by ID
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the order item.
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchasedItem'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/order-items/{id}/cancel':
    post:
      operationId: cancel-order-detail-by-id
      summary: Cancel Order Detail by ID (Admin)
      description: |
        Cancel order detail by ID.

        **Requirements:**
        - Admin can cancel any order detail
        - Order detail must not have status 'preparingForShipment', 'shipped', 'received', or 'completed'

        **What happens when canceled:**
        - Order detail status changes to 'canceled'
        - Product is returned to 'available' status
        - Payment is refunded (if already paid)
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          description: Order Detail ID
          schema:
            type: string
      responses:
        '204':
          description: Successfully canceled order detail
        '400':
          description: Bad Request (order detail cannot be canceled)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden (admin access required)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Order detail not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict (order detail cannot be canceled due to status)
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/order-items/{id}/hold':
    put:
      operationId: put-order-item-hold
      summary: Suspend an order transaction
      description: Places an order item on hold by saving the current status and setting it to "onHold"
      tags:
        - order
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: Order item ID
      responses:
        '204':
          description: Order item successfully placed on hold
        '400':
          description: Bad request - Invalid order status for hold operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Order item not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict - Order item is already on hold
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/order-items/{id}/unhold':
    put:
      operationId: put-order-item-unhold
      summary: Resume an order transaction
      description: Removes an order item from hold by restoring the previous status
      tags:
        - order
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: Order item ID
      responses:
        '204':
          description: Order item successfully removed from hold
        '400':
          description: Bad request - Invalid order status for unhold operation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Order item not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict - Order item is not on hold
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/order-items/{id}/approve':
    put:
      operationId: approve-order-item
      summary: Approve order item
      description: Approve an order item by changing its status from underReview to unshipped
      tags:
        - order
      parameters:
        - name: id
          in: path
          required: true
          description: The unique identifier of the order item to approve.
          schema:
            type: string
      responses:
        '204':
          description: Order item approved successfully
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Order item not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '409':
          description: Conflict - Order item status cannot be approved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /order-details:
    get:
      operationId: get-order-details
      summary: Get Order Details
      description: Retrieve order details with comprehensive filtering capabilities for admin users
      tags:
        - order
      parameters:
        - name: userId
          in: query
          description: Filter by buyer user ID
          schema:
            type: string
        - name: sellerId
          in: query
          description: Filter by seller user ID
          schema:
            type: string
        - name: transactionStatuses
          in: query
          description: Filter by transaction status values (multiple values supported)
          schema:
            type: array
            items:
              type: string
        - name: orderStatuses
          in: query
          description: Filter by order status values (multiple values supported)
          schema:
            type: array
            items:
              type: string
        - name: productId
          in: query
          description: Filter by product ID
          schema:
            type: string
        - name: orderId
          in: query
          description: Filter by order ID
          schema:
            type: string
        - name: from
          in: query
          schema:
            type: string
            format: date-time
            example: '2025-03-13T14:00:00Z'
          description: Filter orders created from this date
        - name: to
          in: query
          schema:
            type: string
            format: date-time
            example: '2025-03-13T14:00:00Z'
          description: Filter orders created until this date
        - name: page
          in: query
          schema:
            type: integer
            default: 1
          description: Page number for pagination.
        - name: limit
          in: query
          schema:
            type: integer
            default: 20
          description: Number of items per page.
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOrderDetailsResponse'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  '/users/{id}/notifications':
    post:
      operationId: create-user-notification
      summary: Create user notification
      description: Create a notification for a user
      tags:
        - user
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
          description: ID of the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserNotificationRequest'
      responses:
        '201':
          description: Successfully created notification
        '400':
          description: Bad Request - Invalid user ID
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - User cannot create notification
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User Not Found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /countries:
    get:
      operationId: get-countries
      summary: Get countries
      description: Get a list of all available countries
      tags:
        - country
      security:
        - bearerAuth: []
      parameters:
        - name: active
          in: query
          description: Active
          required: false
          schema:
            type: boolean
            example: true
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCountriesResponse'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /order-statistics:
    get:
      operationId: get-order-statistics
      summary: Get Order Statistics
      description: 'Retrieve comprehensive order statistics including seller revenue analytics, marketplace VAT obligations, platform fees, and geographical breakdowns for a specific month and year.'
      tags:
        - order-statistics
      parameters:
        - name: year
          in: query
          required: true
          description: 'The year for statistics (e.g., 2024)'
          schema:
            type: integer
            example: 2024
        - name: month
          in: query
          required: true
          description: The month for statistics (1-12)
          schema:
            type: integer
            minimum: 1
            maximum: 12
            example: 3
        - name: countryCode
          in: query
          required: false
          description: 'ISO3 country code for filtering (e.g., "USA", "JPN")'
          schema:
            type: string
            pattern: '^[A-Z]{3}$'
            example: USA
        - name: regionId
          in: query
          required: false
          description: Region ID for geographical filtering
          schema:
            type: string
            example: region_001
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Order statistics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderStatisticsResponse'
              examples:
                march_2024_stats:
                  summary: March 2024 Order Statistics
                  value:
                    sellerRevenue:
                      orderCount: 1250
                      totalOrderAmount: 125000.5
                      totalSalesAmount: 115000.25
                      totalProductPrice: 100000
                      sellerTaxes:
                        - taxAmount: 8000
                          taxRate: 8
                        - taxAmount: 2000.25
                          taxRate: 10
                    marketplaceVAT:
                      vatObligations:
                        - amount: 50000
                          taxAmount: 4000
                          taxRate: 8
                        - amount: 15000
                          taxAmount: 1500
                          taxRate: 10
                    platformFees:
                      memberDiscount: 5000
                      sellingFees: 12500
                      buyingFees: 8750
                    countryBreakdown:
                      - countryCode: USA
                        sellerRevenue:
                          orderCount: 800
                          totalOrderAmount: 80000
                          totalSalesAmount: 75000
                          totalProductPrice: 65000
                          sellerTaxes:
                            - taxAmount: 5200
                              taxRate: 8
                        marketplaceVAT:
                          vatObligations:
                            - amount: 30000
                              taxAmount: 2400
                              taxRate: 8
                        platformFees:
                          memberDiscount: 3000
                          sellingFees: 8000
                          buyingFees: 5500
                      - countryCode: JPN
                        sellerRevenue:
                          orderCount: 450
                          totalOrderAmount: 45000.5
                          totalSalesAmount: 40000.25
                          totalProductPrice: 35000
                          sellerTaxes:
                            - taxAmount: 2800
                              taxRate: 8
                            - taxAmount: 2000.25
                              taxRate: 10
                        marketplaceVAT:
                          vatObligations:
                            - amount: 20000
                              taxAmount: 1600
                              taxRate: 8
                        platformFees:
                          memberDiscount: 2000
                          sellingFees: 4500
                          buyingFees: 3250
                    regionBreakdown:
                      - regionId: region_001
                        regionName: North America
                        sellerRevenue:
                          orderCount: 800
                          totalOrderAmount: 80000
                          totalSalesAmount: 75000
                          totalProductPrice: 65000
                          sellerTaxes:
                            - taxAmount: 5200
                              taxRate: 8
                        marketplaceVAT:
                          vatObligations:
                            - amount: 30000
                              taxAmount: 2400
                              taxRate: 8
                        platformFees:
                          memberDiscount: 3000
                          sellingFees: 8000
                          buyingFees: 5500
        '400':
          description: Bad Request - Invalid parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                invalid_year:
                  summary: Invalid year parameter
                  value:
                    error: Invalid year parameter. Must be between 2020 and 2030
                    code: INVALID_YEAR
                invalid_month:
                  summary: Invalid month parameter
                  value:
                    error: Invalid month parameter. Must be between 1 and 12
                    code: INVALID_MONTH
                invalid_country_code:
                  summary: Invalid country code format
                  value:
                    error: Invalid country code format. Must be 3 uppercase letters
                    code: INVALID_COUNTRY_CODE
        '401':
          description: Unauthorized - Invalid or missing authentication token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Forbidden - Insufficient permissions for admin access
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - No data available for the specified parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              examples:
                no_data:
                  summary: No data found
                  value:
                    error: No order statistics found for the specified year and month
                    code: NO_DATA_FOUND
        '500':
          description: Internal Server Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '503':
          description: Service Unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
tags:
  - name: auth
  - name: system
  - name: cms
  - name: master
  - name: banner
  - name: product
  - name: seller
  - name: upload
  - name: report
  - name: inquiry
  - name: order
  - name: order-statistics
components:
  schemas:
    Pagination:
      type: object
      required:
        - limit
        - page
      properties:
        limit:
          type: integer
          description: number of items per page
          x-oapi-codegen-extra-tags:
            validate: 'required,gte=1'
        page:
          type: integer
          description: number of selected page
          x-oapi-codegen-extra-tags:
            validate: 'required,gte=1'
        totalCount:
          type: integer
          description: number of record
    MaintenanceStatus:
      type: object
      properties:
        isMaintenanceMode:
          type: boolean
          description: Whether the system is currently in maintenance mode
          example: true
        startTime:
          type: string
          format: date-time
          description: When maintenance mode was started (null if not in maintenance)
          example: '2024-01-01T10:00:00Z'
          nullable: true
        endTime:
          type: string
          format: date-time
          description: When maintenance mode was ended (null if still in maintenance)
          example: '2024-01-01T12:00:00Z'
          nullable: true
      required:
        - isMaintenanceMode
    PostalCodesResponse:
      type: object
      description: Response containing list of valid US postal codes
      properties:
        postalCodes:
          type: array
          description: Array of valid US postal codes (5-digit ZIP codes)
          items:
            type: string
            pattern: '^[0-9]{5}$'
            example: '10001'
          example:
            - '10001'
            - '10002'
            - '10003'
            - '90210'
            - '94102'
        count:
          type: integer
          description: Total number of postal codes returned
          example: 41692
      required:
        - postalCodes
        - count
    Admin:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
    ContentCategory:
      type: object
      properties:
        code:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
        contentType:
          $ref: '#/components/schemas/ContentType'
    ContentType:
      type: object
      properties:
        code:
          type: string
        fields:
          type: array
          items:
            $ref: '#/components/schemas/ContentField'
    ContentField:
      type: object
      properties:
        code:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
        type:
          type: string
        required:
          type: boolean
    Content:
      type: object
      properties:
        id:
          type: string
        parentContentId:
          type: string
        title:
          type: object
          additionalProperties:
            type: string
        contentCategoryCode:
          type: string
        description:
          type: string
        content:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ContentContent'
        countryCode:
          type: string
        createdBy:
          $ref: '#/components/schemas/Admin'
        createdAt:
          type: string
        updatedBy:
          $ref: '#/components/schemas/Admin'
        updatedAt:
          type: string
        status:
          $ref: '#/components/schemas/ContentStatus'
        publishedAt:
          type: string
    ContentContent:
      type: object
      properties:
        active:
          type: boolean
        title:
          type: string
        body:
          type: string
        url:
          type: string
        attachments:
          type: array
          items:
            type: string
    TermsType:
      type: string
      enum:
        - communityGuidelines
        - privacyPolicy
        - termsOfUseForGeneral
        - termsOfUseForSeller
        - paymentTerms
        - buyerProtection
        - commercialTransactionLaw
    Terms:
      type: object
      properties:
        countryCode:
          type: string
        active:
          type: boolean
        versionId:
          type: integer
        content:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ContentContent'
        isRequired:
          type: boolean
    TermsBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        type:
          $ref: '#/components/schemas/TermsType'
        terms:
          type: array
          items:
            $ref: '#/components/schemas/Terms'
    User:
      type: object
      properties:
        id:
          type: string
          description: User's unique identifier
          example: 123e4567-e89b-12d3-a456-************
        avatar:
          type: string
          description: User's avatar URL
        email:
          type: string
          description: User's email address
          example: <EMAIL>
        nickname:
          type: string
          description: Nickname of the user
          example: John Doe
        firstName:
          type: string
          description: First name of the user
          example: John
        lastName:
          type: string
          description: Last name of the user
          example: Doe
        gender:
          type: string
          description: Gender of the user
          example: Male
        dateOfBirth:
          type: string
          description: 'Date of birth of the user. Format: YYYY-MM-DD'
          example: '1990-01-01'
        pinSetting:
          type: boolean
          description: Whether the user has set a PIN
          example: true
        countryCode:
          type: string
          description: Country code of the user
          example: us
        regionId:
          type: string
          description: Region ID of the user
          example: 123e4567-e89b-12d3-a456-************
        receiveNewsletter:
          type: boolean
          description: Whether the user wants to receive newsletter
          example: true
        sellerId:
          type: string
          description: Seller ID of the user
          example: 123e4567-e89b-12d3-a456-************
        seller:
          $ref: '#/components/schemas/Seller'
        membership:
          type: integer
        status:
          $ref: '#/components/schemas/AccountStatus'
        accountId:
          type: string
          maxLength: 255
    AccountStatus:
      type: string
      enum:
        - active
        - restricted
        - viewonly
        - suspended
        - inreview
    AccountType:
      type: string
      enum:
        - standard
        - premium
        - seller
    MasterStatus:
      type: string
      enum:
        - draft
        - waitingToPublish
        - published
      x-oapi-codegen-extra-tags:
        validate: 'omitempty,oneof=draft waitingToPublish published'
    ProductCategoriesBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        productCategories:
          type: array
          items:
            $ref: '#/components/schemas/ProductCategory'
    ProductCategory:
      type: object
      properties:
        id:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'omitempty,uuid'
        displayName:
          type: object
          additionalProperties:
            type: string
        active:
          type: boolean
        count:
          type: integer
          description: Number of products in this category
        sizeTable:
          $ref: '#/components/schemas/SizeTable'
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/ProductCategory'
    SizeTable:
      type: string
      enum:
        - general
        - pants
        - shoes
        - hat
        - ring
    BrandsBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        brands:
          type: array
          items:
            $ref: '#/components/schemas/Brand'
    Brand:
      type: object
      properties:
        id:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'omitempty,uuid'
        displayName:
          type: object
          additionalProperties:
            type: string
        active:
          type: boolean
        count:
          type: integer
          description: Number of products for this brand
    LanguagesBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        languages:
          type: array
          items:
            $ref: '#/components/schemas/Language'
    Language:
      type: object
      properties:
        code:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
        active:
          type: boolean
    ProductCondition:
      type: string
      enum:
        - level1
        - level2
        - level3
        - level4
        - level5
        - level6
    Product:
      type: object
      properties:
        id:
          type: string
          description: The unique identifier for the product
        name:
          type: object
          additionalProperties:
            type: string
          description: Name of the product
          example:
            en: iPhone 15
            ko: 아이폰 15
        categoryId:
          type: string
          description: Category of the product
        categories:
          type: array
          items:
            $ref: '#/components/schemas/ProductCategory'
        target:
          $ref: '#/components/schemas/ProductTarget'
        size:
          type: string
          description: Size information (if applicable)
        brandId:
          type: string
          description: Brand of the product
        brand:
          $ref: '#/components/schemas/ProductBrand'
        brandNameOther:
          type: string
          description: Brand other of the product in case of not in the list
          example: Apple
        condition:
          $ref: '#/components/schemas/ProductCondition'
        description:
          type: object
          additionalProperties:
            type: string
          description: Detailed description of the product
          example:
            en: Latest Apple iPhone with A16 Bionic chip.
            ko: 최신 Apple iPhone A16 Bionic 칩을 탑재한 제품입니다.
        images:
          type: array
          description: List of product images
          items:
            type: string
        releaseDate:
          type: string
          description: Timestamp of when the product was released
          example: '2025-03-13T14:00:00Z'
        salesStatus:
          $ref: '#/components/schemas/ProductSalesStatus'
        transactionStatus:
          $ref: '#/components/schemas/ProductTransactionStatus'
        price:
          type: number
          format: double
          description: Price of the product
          example: 999.99
        isDiscount:
          type: boolean
          description: Does the product have a discount?
          example: true
        discountAmount:
          type: number
          format: double
          description: Discount amount applied to the product
          example: 30000
          minimum: 0
        discountRate:
          type: number
          format: double
          description: Discount percentage applied to the product
          example: 10.5
          minimum: 0
          maximum: 100
        isFavorite:
          type: boolean
          description: Whether the product is in the user's favorites
          example: true
        shipFrom:
          type: string
          description: Country of the product
          example: KR
        shippingMethod:
          type: string
          description: Shipping method of the product
        sizeDetails:
          type: string
        createdAt:
          type: string
          format: date-time
        orderItem:
          $ref: '#/components/schemas/PurchasedItem'
        isCITESRestricted:
          type: boolean
          description: Whether the product is subject to CITES restrictions
          example: true
        citesInformation:
          $ref: '#/components/schemas/CitesCommonField'
    ProductTarget:
      type: string
      enum:
        - men
        - women
        - unisex
        - children
    CitesCommonField:
      type: object
      properties:
        productDetailsForReview:
          type: string
          description: Product details required for review
          example: Detailed information about the product's material and origin
        proofOfLegalAcquisition:
          type: array
          description: List of documents proving legal acquisition
          items:
            type: string
          example:
            - certificate1.pdf
            - invoice123.jpg
        citesExportPermit:
          type: array
          description: List of CITES export permit documents
          items:
            type: string
          example:
            - export_permit_001.pdf
        domesticTradingCertificates:
          type: array
          description: Other certificates or transaction documents required for domestic trading in the listing country
          items:
            type: string
          example:
            - domestic_trade_cert_01.pdf
    ProductSalesStatus:
      type: string
      enum:
        - draft
        - onSale
        - sold
        - suspended
        - available
    ProductTransactionStatus:
      type: string
      enum:
        - preparingForShipment
        - shipped
        - completed
        - onHold
        - unshipped
        - underReview
        - canceled
        - received
    ProductBrand:
      type: object
      properties:
        id:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
    BannerActionType:
      type: string
      enum:
        - query
        - url
        - inAppUrl
        - shop
        - noAction
      description: Type of banner action
    BannerAction:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/BannerActionType'
        query:
          $ref: '#/components/schemas/BannerQuery'
        url:
          type: string
          description: URL to redirect to
        inAppUrl:
          type: string
          description: In-app URL to redirect to
        shop:
          type: string
          description: Shop ID to redirect to
    BannerQuery:
      type: object
      properties:
        targets:
          type: array
          items:
            $ref: '#/components/schemas/ProductTarget'
        categoryIds:
          type: array
          items:
            type: string
            description: Category ID
        brandNames:
          type: array
          items:
            type: string
            description: Brand name
        minPrice:
          type: number
          format: double
          description: Minimum price to search for
        maxPrice:
          type: number
          format: double
          description: Maximum price to search for
        conditions:
          type: array
          items:
            type: string
            description: Condition
        sizes:
          type: array
          items:
            type: string
            description: Size
        countryCodes:
          type: array
          items:
            type: string
            description: Country code
        saleStatuses:
          type: array
          items:
            $ref: '#/components/schemas/ProductSalesStatus'
        keyword:
          type: string
          description: Keyword to search for
    BannerTranslation:
      type: object
      description: additionalProperty is language code
      properties:
        status:
          $ref: '#/components/schemas/BannerStatus'
        title:
          type: string
        position:
          type: array
          items:
            type: string
            description: position
        bannerImageUrl:
          type: string
        heroImageUrl:
          type: string
    BannerStatus:
      type: string
      enum:
        - published
        - draft
    Banner:
      type: object
      properties:
        id:
          type: string
        status:
          $ref: '#/components/schemas/BannerStatus'
        action:
          $ref: '#/components/schemas/BannerAction'
        publishDate:
          type: string
        countryCodes:
          type: array
          items:
            type: string
            description: Country code
        bannerTranslations:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/BannerTranslation'
        createdBy:
          $ref: '#/components/schemas/Admin'
        createdAt:
          type: string
        updatedBy:
          $ref: '#/components/schemas/Admin'
        updatedAt:
          type: string
    OrderBy:
      type: string
      enum:
        - asc
        - desc
    UiWordsBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        ui_words_csv:
          $ref: '#/components/schemas/UiWordsCsv'
    UiWordsCsv:
      type: object
      required:
        - fileName
        - path
        - count
        - languages
        - items
      properties:
        fileName:
          type: string
        path:
          type: string
        count:
          type: integer
        languages:
          type: array
          items:
            type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/UiWords'
    UiWords:
      type: object
      properties:
        key:
          type: string
        value:
          type: object
          additionalProperties:
            type: string
    NotificationSettingsType:
      type: string
      enum:
        - all
        - campaigns
        - important
    NotificationType:
      type: string
      description: Type of the notification
      enum:
        - IMPORTANT_SYSTEM
        - CAMPAIGN_PROMOTION
    Email:
      type: object
      properties:
        email:
          type: string
    Notification:
      type: object
      description: Notification model representing a single notification
      properties:
        id:
          type: string
          description: Unique identifier for the notification
        createdAt:
          type: string
          description: Timestamp when the notification was created
        updatedAt:
          type: string
          description: Timestamp when the notification was updated
        publishedAt:
          type: string
          description: Timestamp when the notification was published
        status:
          $ref: '#/components/schemas/ContentStatus'
        title:
          type: object
          description: Title of the notification
          additionalProperties:
            type: string
        type:
          $ref: '#/components/schemas/NotificationType'
        countryCodes:
          type: array
          items:
            type: string
        content:
          type: object
          description: Content of the notification
          additionalProperties:
            $ref: '#/components/schemas/NotificationArticleContent'
    NotificationArticleContent:
      type: object
      description: Content of the notification
      properties:
        active:
          type: boolean
          description: Indicates if the notification is active
        title:
          type: string
          description: Title of the notification
        message:
          type: string
          description: Message of the notification
        keyImage:
          type: string
          description: Key image of the notification
        articleImages:
          type: array
          items:
            type: string
          description: Article images of the notification
    ContentStatus:
      type: string
      enum:
        - draft
        - published
      x-oapi-codegen-extra-tags:
        validate: 'omitempty,oneof=draft published'
    CustomNGWordsBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        customNGWords:
          type: array
          items:
            $ref: '#/components/schemas/CustomNGWord'
    CustomNGWord:
      type: object
      properties:
        word:
          type: string
        active:
          type: boolean
    Seller:
      type: object
      description: 'Comprehensive seller profile model representing a user''s seller account, including associated user information, store details, and seller-specific metadata'
      required:
        - accountType
        - shopName
      properties:
        id:
          type: string
          description: Seller ID
        accountId:
          type: string
          description: Account ID
        accountType:
          type: string
          enum:
            - individual
            - bussiness
          description: Validated account types
        stockLocation:
          type: string
          description: Stock locaiton. (Ship from)
          example: Japan
        shopName:
          type: string
          description: Shop name
        about:
          type: object
          additionalProperties:
            type: string
          description: Something about shop
        specialty:
          type: object
          additionalProperties:
            type: string
          description: Specialty
        favoriteBrands:
          type: array
          description: List of favorite brands
          items:
            type: string
            description: Name of brand
        avatarUrl:
          type: string
          description: 'Url to avatar (S3, CDN, or any...)'
        headerImgUrl:
          type: string
          description: 'Url to header image (S3, CDN, or any...)'
        isFavorite:
          type: boolean
          description: Whether the seller is a favorite of the user
        paypalId:
          type: string
          description: PayPal payer ID from billing agreement token
    SellerRating:
      type: object
      description: Seller rating object
      properties:
        avgRating:
          type: number
          format: double
        avgPoliteRating:
          type: number
          format: double
        avgSpeedRating:
          type: number
          format: double
        avgPackRating:
          type: number
          format: double
        totalOrders:
          type: integer
        avgDaysToShip:
          type: number
          format: double
    Report:
      type: object
      properties:
        id:
          type: string
        status:
          $ref: '#/components/schemas/ReportStatus'
        detail:
          type: string
        reporterId:
          type: string
        reporterType:
          type: string
          enum:
            - user
            - seller
        reporter:
          oneOf:
            - $ref: '#/components/schemas/User'
            - $ref: '#/components/schemas/Seller'
        itemId:
          type: string
        itemType:
          $ref: '#/components/schemas/ReportItemType'
        item:
          oneOf:
            - $ref: '#/components/schemas/Product'
            - $ref: '#/components/schemas/User'
            - $ref: '#/components/schemas/Seller'
        createdAt:
          type: string
        updatedAt:
          type: string
    ReportStatus:
      type: string
      enum:
        - notHandled
        - inProgress
        - pending
        - completed
        - uncompleted
    ReportItemType:
      type: string
      enum:
        - product
        - user
        - seller
    FaqCategory:
      type: object
      properties:
        id:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'omitempty,uuid'
        displayName:
          type: object
          additionalProperties:
            type: string
        active:
          type: boolean
        count:
          type: integer
        subCategories:
          type: array
          items:
            $ref: '#/components/schemas/FaqCategory'
    FaqCategoriesBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/ContentStatus'
        faqCategories:
          type: array
          items:
            $ref: '#/components/schemas/FaqCategory'
    Address:
      type: object
      properties:
        isDifferentFromResidence:
          type: boolean
          description: Whether the address is different from the residence address
        firstName:
          type: string
          description: First name of the user
          example: John
        lastName:
          type: string
          description: Last name of the user
          example: Doe
        fullName:
          type: string
          description: Full name of the user
          example: John Doe
        address1:
          type: string
          description: Address line 1
          example: 123 Main St
        address2:
          type: string
          description: Address line 2
          example: Apt 1
        city:
          type: string
          description: City of the address
          example: New York
        state:
          type: string
          description: State of the address
          example: NY
        country:
          type: string
          description: Country of the address
          example: USA
        postalCode:
          type: string
          description: Postal code of the address
          example: '10001'
        phone:
          $ref: '#/components/schemas/Phone'
        mobile:
          $ref: '#/components/schemas/Phone'
        regionId:
          type: string
          description: Region id of the address
    Phone:
      type: object
      required:
        - countryCode
        - number
      properties:
        countryCode:
          type: string
          description: The country code of the phone number without the plus sign (+) (e.g. in Japan it is 81)
        number:
          type: string
    CreditCard:
      type: object
      properties:
        brand:
          type: string
        cardNumber:
          type: string
        expiryYear:
          type: string
        expiryMonth:
          type: string
        cardHolderName:
          type: string
    SubscriptionFeesBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        subscriptionFees:
          type: array
          items:
            $ref: '#/components/schemas/SubscriptionFee'
    SubscriptionFee:
      type: object
      properties:
        languageCode:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
        type:
          type: string
        currency:
          type: string
        price:
          type: number
          format: double
    CommissionSalesBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        commissionSales:
          type: array
          items:
            $ref: '#/components/schemas/CommissionSale'
    CommissionSale:
      type: object
      properties:
        range:
          type: number
        min:
          type: number
          format: double
        max:
          type: number
          format: double
        fee:
          type: number
          format: double
    TransferFeesBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        transferFees:
          type: array
          items:
            $ref: '#/components/schemas/TransferFee'
    TransferFee:
      type: object
      properties:
        languageCode:
          type: string
        displayName:
          type: object
          additionalProperties:
            type: string
        price:
          type: number
          format: double
    InquiryLocationStats:
      type: object
      properties:
        country:
          type: string
        countryCount:
          type: integer
        state:
          type: string
        stateCount:
          type: integer
        city:
          type: string
        cityCount:
          type: integer
    PurchaseFeesBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        purchaseFees:
          type: array
          items:
            $ref: '#/components/schemas/PurchaseFee'
    PurchaseFee:
      type: object
      properties:
        type:
          $ref: '#/components/schemas/PurchaseFeeType'
        range:
          type: integer
        min:
          type: number
          format: double
        max:
          type: number
          format: double
        adminFeeRate:
          type: number
          format: double
        purchaseFee:
          type: number
          format: double
    PurchaseFeeType:
      type: string
      enum:
        - regular
        - premium
    Order:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        orderNumber:
          type: string
        amount:
          type: number
          format: double
        totalAmount:
          type: number
          format: double
        taxAmount:
          type: number
          format: double
        taxRate:
          type: number
          format: double
        administrativeFee:
          type: number
          format: double
        administrativeFeeRate:
          type: number
          format: double
        administrativeFeeTaxRate:
          type: number
          format: double
        administrativeFeeTaxAmount:
          type: number
          format: double
        purchaseFee:
          type: number
          format: double
        purchaseFeeTaxRate:
          type: number
          format: double
        purchaseFeeTaxAmount:
          type: number
          format: double
        isBan:
          type: boolean
        items:
          type: array
          items:
            $ref: '#/components/schemas/PurchasedItem'
        paymentMethod:
          $ref: '#/components/schemas/CreditCard'
        taxDetails:
          type: array
          items:
            $ref: '#/components/schemas/TaxDetail'
        shippingAddress:
          $ref: '#/components/schemas/Address'
        taxInfo:
          $ref: '#/components/schemas/TaxInfo'
        user:
          $ref: '#/components/schemas/User'
        completedAt:
          type: string
    PurchasedItem:
      type: object
      properties:
        id:
          type: string
        orderId:
          type: string
        transactionStatus:
          $ref: '#/components/schemas/ProductTransactionStatus'
        createdAt:
          type: string
          format: date-time
        productId:
          type: string
        product:
          $ref: '#/components/schemas/Product'
        seller:
          $ref: '#/components/schemas/Seller'
        waybillNumber:
          type: string
        amount:
          type: number
          format: double
        taxAmount:
          type: number
          format: double
        taxRate:
          type: number
          format: double
        totalAmount:
          type: number
          format: double
        taxInfos:
          type: array
          items:
            $ref: '#/components/schemas/TaxItemInfo'
        orderEnd:
          type: string
        orderNumber:
          type: string
        shippedDate:
          type: string
          format: date-time
        completedDate:
          type: string
          format: date-time
        note:
          type: string
        order:
          $ref: '#/components/schemas/Order'
        discountAmount:
          type: number
          format: double
        rating:
          $ref: '#/components/schemas/Rating'
    TaxDetail:
      type: object
      properties:
        amount:
          type: number
          format: double
        taxRate:
          type: number
          format: double
        taxAmount:
          type: number
          format: double
    Currency:
      type: object
      properties:
        code:
          type: string
          description: Currency code
          example: USD
          enum:
            - USD
            - EUR
            - JPY
            - GBP
            - KRW
            - HKD
            - AUD
            - AED
            - SAR
            - SGD
        isBankAccountCurrency:
          type: boolean
          description: Currency is bank account currency
          example: true
    Country:
      type: object
      properties:
        active:
          type: boolean
        code:
          type: string
          description: Country branch code
          example: US
        displayName:
          type: object
          additionalProperties:
            type: string
          description: Country branch display name
          example:
            en: United States
        saletaxDatabase:
          $ref: '#/components/schemas/SaletaxDatabase'
        regions:
          type: array
          items:
            $ref: '#/components/schemas/RegionTax'
        currencies:
          type: array
          items:
            $ref: '#/components/schemas/Currency'
        isOnlyForCorporateMember:
          type: boolean
          description: Country branch is only for corporate member
          example: true
        bankAccountType:
          $ref: '#/components/schemas/BankAccountType'
        taxName:
          $ref: '#/components/schemas/TaxName'
        taxConfig:
          $ref: '#/components/schemas/TaxConfig'
    CountryBranch:
      type: object
      properties:
        id:
          type: string
        createdAt:
          type: string
        updatedAt:
          type: string
        publishedAt:
          type: string
        status:
          $ref: '#/components/schemas/MasterStatus'
        country:
          $ref: '#/components/schemas/Country'
    BankAccountType:
      type: string
      enum:
        - SWIFT_CODE
        - IBAN
        - JAPAN_ACCOUNT
        - UAE_ACCOUNT
    TaxName:
      type: string
      enum:
        - CONSUMPTION_TAX
        - SALES_TAX
        - VAT
        - GST
    OptionalTax:
      type: object
      properties:
        categoryIds:
          type: array
          items:
            type: string
          description: Category IDs
        taxRate:
          type: number
          format: double
          description: Tax rate
          example: 0.1
        minAmount:
          type: number
          format: double
          description: Min
          example: 0.1
        maxAmount:
          type: number
          format: double
          description: Max
          example: 0.1
        isOverflow:
          type: boolean
          description: Is overflow
          example: true
    TaxConfig:
      type: object
      properties:
        taxRate:
          type: number
          format: double
          description: Tax rate
          example: 0.1
        optionalTax:
          type: array
          items:
            $ref: '#/components/schemas/OptionalTax'
        isMarketplaceOperators:
          type: boolean
          description: Is marketplace operators
          example: true
        isAdministrativeFeeTax:
          type: boolean
          description: Is administrative fee tax
          example: true
        isPurchaseFeeTax:
          type: boolean
          description: Is purchase fee tax
          example: true
        isSalesCommissionFeeTax:
          type: boolean
          description: Is sales commission fee tax
          example: true
        isRemittanceFeeTax:
          type: boolean
          description: Is sales commission fee tax
          example: true
    RegionTax:
      type: object
      properties:
        id:
          type: string
        active:
          type: boolean
        code:
          type: string
          description: Region code
          example: CA
        displayName:
          type: object
          additionalProperties:
            type: string
          description: Region display name
        regions:
          type: array
          items:
            $ref: '#/components/schemas/RegionTax'
        taxConfig:
          $ref: '#/components/schemas/TaxConfig'
    DistrictTax:
      type: object
      properties:
        zipCode:
          type: string
          description: Zip code
          example: '90001'
        taxRegionName:
          type: string
          description: District
          example: Manhattan
        specialRate:
          type: number
          format: double
          description: Special rate
          example: 0.1
    SaletaxDatabase:
      type: object
      properties:
        statesCount:
          type: integer
          description: States count
          example: 50
        countiesCount:
          type: integer
          description: Counties count
          example: 100
        citiesCount:
          type: integer
          description: Cities count
          example: 1000
        recordsCount:
          type: integer
          description: Records count
          example: 10000
        districtTaxes:
          type: array
          items:
            $ref: '#/components/schemas/DistrictTax'
    TaxInfo:
      type: object
      description: Tax information for an order
      properties:
        taxName:
          $ref: '#/components/schemas/TaxName'
        country:
          $ref: '#/components/schemas/Country'
        regions:
          type: array
          items:
            $ref: '#/components/schemas/RegionTax'
        taxDetails:
          type: array
          items:
            $ref: '#/components/schemas/TaxDetail'
    TaxItemInfo:
      type: object
      description: Tax information for an order
      properties:
        taxName:
          $ref: '#/components/schemas/TaxName'
        country:
          $ref: '#/components/schemas/Country'
        region:
          $ref: '#/components/schemas/RegionTax'
        district:
          $ref: '#/components/schemas/RegionTax'
        taxDetails:
          type: array
          items:
            $ref: '#/components/schemas/TaxDetail'
    Rating:
      type: object
      properties:
        rating:
          type: number
          format: double
        politeRating:
          type: number
          format: double
        speedRating:
          type: number
          format: double
        packRating:
          type: number
          format: double
    CompanyInfo:
      type: object
      description: Validation model for indentity information
      properties:
        companyName:
          type: string
          description: Company name
          example: Devdev Inc.
        contactName:
          type: string
          description: Contact person name
          example: John Doe
        email:
          type: string
          description: Company/Contact person's email address
          example: <EMAIL>
        phoneNumber:
          type: string
          pattern: '^\+?[1-9]\d{1,14}$'
          description: Phone number with international format validation
        dunsNumber:
          type: string
          description: DUNS number
          example: '**********'
        websiteURL:
          type: string
          description: Website URL
          example: 'https://www.example.com'
    BankAccount:
      type: object
      description: Validation model for bank account profile information
      required:
        - bankName
        - accountNumber
        - accountHolderName
        - accountType
      properties:
        country:
          type: string
          description: Country/Region
        accountHolderName:
          type: string
          minLength: 2
          maxLength: 100
          description: Account holder name validation
        accountHolderKatakana:
          type: string
          description: Account holder name in katakana validation (JP only)
        bankCode:
          type: string
          pattern: '^\d{3,6}$'
          description: Bank code format validation
        bankName:
          type: string
          minLength: 2
          maxLength: 100
          description: Bank name with length constraints
        branchCode:
          type: string
          pattern: '^\d{3,6}$'
          description: Branch code format validation
        branchName:
          type: string
          minLength: 2
          maxLength: 100
        accountType:
          type: string
          enum:
            - savings
            - checking
            - corporate
          description: Validated account types
        routingNumber:
          type: string
          description: Routing number
        accountNumber:
          type: string
          pattern: '^\d{8,20}$'
          description: Account number format validation
        iban:
          type: string
          description: IBAN code
        swiftCode:
          type: string
          pattern: '^[A-Z]{4}[A-Z]{2}[A-Z0-9]{2}([A-Z0-9]{3})?$'
          description: SWIFT/BIC code format validation
        pin:
          type: string
          description: PIN for bank account
          example: '1234'
    Kyc:
      type: object
      description: Validation model for indentity information
      properties:
        kycType:
          type: string
          enum:
            - passport
            - driverlicense
          description: Proof of indentity
        kycImgUrl:
          type: string
          description: 'Url to KYC image (S3, CDN, or any...)'
    NotificationContent:
      type: object
      description: Content message of the notification
      properties:
        message:
          type: string
          description: Message of the notification
    UserStatistics:
      type: object
      description: User statistics summary containing aggregated data
      required:
        - totalUsers
        - activeUsers
        - sellerUsers
        - premiumUsers
        - generalUsers
      properties:
        displayName:
          type: object
          additionalProperties:
            type: string
          description: Display name
          example:
            en: United States
        totalUsers:
          type: integer
          description: Total number of users
          example: 15000
        activeUsers:
          type: integer
          description: Number of active users
          example: 12000
        sellerUsers:
          type: integer
          description: Number of seller users
          example: 500
        premiumUsers:
          type: integer
          description: Number of premium users
          example: 2000
        generalUsers:
          type: integer
          description: Number of general users
          example: 12500
        countryBreakdown:
          type: array
          description: Statistics breakdown by country (only populated for global summary)
          items:
            $ref: '#/components/schemas/CountryStatistics'
        regionBreakdown:
          type: array
          description: Statistics breakdown by region (only populated for country/region summaries)
          items:
            $ref: '#/components/schemas/RegionStatistics'
    CountryStatistics:
      type: object
      description: User statistics for a specific country
      required:
        - country
        - totalUsers
        - activeUsers
        - sellerUsers
        - premiumUsers
        - generalUsers
      properties:
        country:
          type: string
          description: ISO3 country code
          example: USA
        displayName:
          type: object
          additionalProperties:
            type: string
          description: Country display name
          example:
            en: United States
        totalUsers:
          type: integer
          description: Total number of users in this country
          example: 5000
        activeUsers:
          type: integer
          description: Number of active users in this country
          example: 4000
        sellerUsers:
          type: integer
          description: Number of seller users in this country
          example: 200
        premiumUsers:
          type: integer
          description: Number of premium users in this country
          example: 800
        generalUsers:
          type: integer
          description: Number of general users in this country
          example: 4000
    RegionStatistics:
      type: object
      description: User statistics for a specific region
      required:
        - regionId
        - totalUsers
        - activeUsers
        - sellerUsers
        - premiumUsers
        - generalUsers
      properties:
        regionId:
          type: string
          description: Unique identifier for the region
          example: region_123
        displayName:
          type: object
          additionalProperties:
            type: string
          description: Region display name
          example:
            en: United States
        totalUsers:
          type: integer
          description: Total number of users in this region
          example: 1000
        activeUsers:
          type: integer
          description: Number of active users in this region
          example: 800
        sellerUsers:
          type: integer
          description: Number of seller users in this region
          example: 50
        premiumUsers:
          type: integer
          description: Number of premium users in this region
          example: 150
        generalUsers:
          type: integer
          description: Number of general users in this region
          example: 800
    AuthRequest:
      type: object
      description: Auth Request
      required:
        - grantType
      properties:
        grantType:
          type: string
          enum:
            - password
            - refreshToken
          x-oapi-codegen-extra-tags:
            validate: oneof=password refreshToken
        username:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'omitempty,gt=0'
        password:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'omitempty,gt=0'
        refreshToken:
          type: string
          x-oapi-codegen-extra-tags:
            validate: 'omitempty,gt=0'
    MaintenanceUpdateRequest:
      type: object
      properties:
        isMaintenanceMode:
          type: boolean
          description: 'Set to true to enable maintenance mode, false to disable'
          example: true
      required:
        - isMaintenanceMode
    PublishProductCategoriesBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the product categories branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    PublishBrandsBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the brands branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    PublishLanguagesBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the languages branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    PublishUiWordsBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the ui words branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    PublishTermsBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the terms branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    PublishCountryBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the country branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    SearchProductsRequest:
      type: object
      description: Search products request object
      properties:
        action:
          $ref: '#/components/schemas/BannerAction'
        pagination:
          $ref: '#/components/schemas/Pagination'
    ProductUpdateRequest:
      type: object
      description: Product request update object
      properties:
        name:
          type: object
          additionalProperties:
            type: string
          description: Name of the product
          example:
            en: iPhone 15
            ko: 아이폰 15
        description:
          type: object
          additionalProperties:
            type: string
          description: Description of the product
        categoryId:
          type: string
          description: Category of the product
        target:
          type: string
          description: Target
          enum:
            - men
            - women
            - unisex
            - children
          example: men
        size:
          type: string
          description: Size information (if applicable)
        brandId:
          type: string
          description: Brand of the product
        condition:
          type: string
          description: Condition of the product
          enum:
            - level1
            - level2
            - level3
            - level4
            - level5
            - level6
        images:
          type: array
          description: List of product images
          items:
            type: string
        releaseDate:
          type: string
          description: Timestamp of when the product was released
          format: date-time
          example: '2025-03-13T14:00:00Z'
        saleStatus:
          type: string
          description: Status of the product
          enum:
            - draft
            - onSale
            - sold
            - suspended
          example: public
        price:
          type: number
          format: double
          description: Price of the product
          example: 999.99
        isDiscount:
          type: boolean
          description: Does the product have a discount?
          example: true
        discountRate:
          type: number
          format: double
          description: Discount percentage applied to the product
          example: 10.5
          minimum: 0
          maximum: 100
        discountAmount:
          type: number
          format: double
          description: Discount amount applied to the product
          example: 30000
          minimum: 0
    ProductBanRequest:
      type: object
      description: Product request ban object
      properties:
        isBanned:
          type: boolean
    UploadFileRequest:
      type: object
      properties:
        file:
          type: string
          format: binary
          description: The file to upload
    UserUpdateRequest:
      type: object
      description: User request update object
      properties:
        email:
          type: string
          description: User's email address
          example: <EMAIL>
        nickname:
          type: string
          description: Nickname of the user
          example: John Doe
        firstName:
          type: string
          description: First name of the user
          example: John
        lastName:
          type: string
          description: Last name of the user
          example: Doe
        gender:
          type: string
          description: Gender of the user
          example: Male
        dateOfBirth:
          type: string
          description: 'Date of birth of the user. Format: YYYY-MM-DD'
          example: '1990-01-01'
        language:
          type: string
        countryCode:
          type: string
        regionId:
          type: string
          format: uuid
        homeAddress:
          $ref: '#/components/schemas/Address'
          description: Home address of the user
        shippingAddress:
          $ref: '#/components/schemas/Address'
          description: Shipping address of the user
        billingAddress:
          $ref: '#/components/schemas/Address'
          description: Billing address of the user
        creditCard:
          $ref: '#/components/schemas/CreditCard'
    UserStatusUpdateRequest:
      type: object
      description: User status update request object
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/AccountStatus'
          description: The new status for the user account
          example: suspended
    PublishCommissionSalesBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the commission sales branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    PublishTransferFeesBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the transfer fees branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    PublishPurchaseFeesBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the purchase fees branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    BanOrderRequest:
      type: object
      description: Ban request  object
      properties:
        isBan:
          type: boolean
          description: default false
    ErrorResponse:
      type: object
      description: Response when abnormal
      properties:
        code:
          type: string
          description: Error code
        message:
          type: string
          description: Error message
        request_id:
          type: string
          description: Request id where the error occurred
        debug_message:
          type: string
          description: Debug message (for development)
    AuthResponse:
      type: object
      description: Auth Response
      required:
        - accessToken
        - refreshToken
      properties:
        accessToken:
          type: string
          description: Access token
        refreshToken:
          type: string
          description: Refresh token
    SearchUsersResponse:
      type: object
      description: Search users response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/User'
        pagination:
          $ref: '#/components/schemas/Pagination'
    SearchBannersResponse:
      type: object
      description: Search banners response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Banner'
        pagination:
          $ref: '#/components/schemas/Pagination'
    SearchProductsResponse:
      type: object
      description: Search banner products response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Product'
        pagination:
          $ref: '#/components/schemas/Pagination'
    SellerProductsResponse:
      type: object
      description: Get products response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Product'
        pagination:
          $ref: '#/components/schemas/Pagination'
    UploadFileResponse:
      type: object
      properties:
        key:
          type: string
          description: The key or path of the file
      required:
        - key
    GetListEmailResponse:
      type: object
      description: Get List Emails
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Email'
    GetNotificationsResponse:
      type: object
      description: Get notifications response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Notification'
        pagination:
          $ref: '#/components/schemas/Pagination'
    PublishCustomNGWordsBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the custom ng words branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    GetReportsResponse:
      type: object
      description: Get reports response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Report'
        pagination:
          $ref: '#/components/schemas/Pagination'
    PublishSubscriptionFeesBranchRequest:
      type: object
      required:
        - publishedAt
      properties:
        publishedAt:
          type: string
          description: The date and time the subscription fees branch was published (YYYY-MM-DD)
          x-oapi-codegen-extra-tags:
            validate: 'required,dateOnly'
    GetInquiryStatsAreaServiceResponse:
      type: object
      description: Get inquiry stats area service response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/InquiryLocationStats'
        pagination:
          $ref: '#/components/schemas/Pagination'
    GetOrderItemResponse:
      type: object
      description: Get Order Item response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/PurchasedItem'
    GetUserByIdResponse:
      type: object
      description: User response get object
      properties:
        id:
          type: string
          description: User's unique identifier
          example: 123e4567-e89b-12d3-a456-************
        sellerId:
          type: string
          description: Seller ID of the user
          example: 123e4567-e89b-12d3-a456-************
        name:
          type: string
          description: Name of the user
          example: John Doe
        email:
          type: string
          description: User's email address
          example: <EMAIL>
        phoneNumber:
          type: string
          description: User's phone number
          example: '+**********'
        createdAt:
          type: string
        updatedAt:
          type: string
        deletedAt:
          type: string
        firstName:
          type: string
          description: First name of the user
          example: John
        lastName:
          type: string
          description: Last name of the user
          example: Doe
        nickname:
          type: string
          description: Nickname of the user
          example: John Doe
        gender:
          type: string
          description: Gender of the user
          example: Male
        dateOfBirth:
          type: string
          description: 'Date of birth of the user. Format: YYYY-MM-DD'
          example: '1990-01-01'
        pinSetting:
          type: boolean
          description: Whether the user has set a PIN
          example: true
        countryCode:
          type: string
          description: Country code of the user
          example: us
        regionId:
          type: string
          description: Region ID of the user
          example: 123e4567-e89b-12d3-a456-************
        receiveNewsletter:
          type: boolean
          description: Whether the user wants to receive newsletter
          example: true
        accountId:
          type: string
          maxLength: 255
        status:
          type: string
          maxLength: 50
        membership:
          type: integer
        confirmTermDate:
          type: string
          description: 'Date of confirmation of the user. Format: YYYY-MM-DD'
          example: '2021-01-01'
        isTermsRequired:
          type: boolean
          description: Whether the user has confirmed the terms and conditions
        language:
          type: string
        homeAddress:
          $ref: '#/components/schemas/Address'
          description: Home address of the user
        shippingAddress:
          $ref: '#/components/schemas/Address'
          description: Shipping address of the user
        billingAddress:
          $ref: '#/components/schemas/Address'
          description: Billing address of the user
        creditCard:
          $ref: '#/components/schemas/CreditCard'
        seller:
          $ref: '#/components/schemas/Seller'
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
    GetOrderResponse:
      type: object
      description: Get Order Item response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Order'
        pagination:
          $ref: '#/components/schemas/Pagination'
    GetOrderDetailsResponse:
      type: object
      description: Get Order Details response object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/PurchasedItem'
        pagination:
          $ref: '#/components/schemas/Pagination'
    GetSellerProfileResponse:
      type: object
      description: Seller profile information for admin
      required:
        - seller
      properties:
        seller:
          $ref: '#/components/schemas/Seller'
        companyInfo:
          $ref: '#/components/schemas/CompanyInfo'
        kyc:
          $ref: '#/components/schemas/Kyc'
        address:
          $ref: '#/components/schemas/Address'
        bankAccount:
          $ref: '#/components/schemas/BankAccount'
        rating:
          $ref: '#/components/schemas/SellerRating'
    CreateUserNotificationRequest:
      type: object
      description: User request create notification object
      properties:
        content:
          type: object
          description: Content of the notification with language support
          additionalProperties:
            $ref: '#/components/schemas/NotificationContent'
    GetCountriesResponse:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/Country'
    GlobalUserSummaryResponse:
      type: object
      description: Global user summary response
      required:
        - summary
      properties:
        summary:
          $ref: '#/components/schemas/UserStatistics'
    CountryUserSummaryResponse:
      type: object
      description: Country user summary response
      required:
        - summary
      properties:
        summary:
          $ref: '#/components/schemas/UserStatistics'
    RegionUserSummaryResponse:
      type: object
      description: Region user summary response
      required:
        - summary
      properties:
        summary:
          $ref: '#/components/schemas/UserStatistics'
    OrderStatistics:
      type: object
      description: Comprehensive order statistics data
      required:
        - sellerRevenue
        - marketplaceVAT
        - platformFees
        - countryBreakdown
        - regionBreakdown
      properties:
        sellerRevenue:
          $ref: '#/components/schemas/SellerRevenue'
        marketplaceVAT:
          $ref: '#/components/schemas/MarketplaceVAT'
        platformFees:
          $ref: '#/components/schemas/PlatformFees'
        countryBreakdown:
          type: array
          description: Statistics broken down by country
          items:
            $ref: '#/components/schemas/CountryBreakdown'
        regionBreakdown:
          type: array
          description: Statistics broken down by region
          items:
            $ref: '#/components/schemas/RegionBreakdown'
    SellerRevenue:
      type: object
      description: Seller revenue analytics data
      required:
        - orderCount
        - totalOrderAmount
        - totalSalesAmount
        - totalProductPrice
        - sellerTaxes
      properties:
        orderCount:
          type: integer
          description: Total number of orders
          minimum: 0
          example: 1250
        totalOrderAmount:
          type: number
          format: double
          description: Total price of all orders from seller
          minimum: 0
          example: 125000.5
        totalSalesAmount:
          type: number
          format: double
          description: Confirmed revenue including taxes for seller
          minimum: 0
          example: 115000.25
        totalProductPrice:
          type: number
          format: double
          description: Total price of products sold by seller
          minimum: 0
          example: 100000
        sellerTaxes:
          type: array
          description: 'Taxes that seller must pay, grouped by tax rate'
          items:
            $ref: '#/components/schemas/SellerTax'
    SellerTax:
      type: object
      description: Seller tax information grouped by tax rate
      required:
        - taxAmount
        - taxRate
      properties:
        taxAmount:
          type: number
          format: double
          description: Tax amount for this rate
          minimum: 0
          example: 8000
        taxRate:
          type: number
          format: double
          description: 'Tax rate percentage (e.g., 8 for 8%)'
          minimum: 0
          maximum: 100
          example: 8
    MarketplaceVAT:
      type: object
      description: Marketplace VAT obligations data
      required:
        - vatObligations
      properties:
        vatObligations:
          type: array
          description: 'Revenue from orders where MP must pay VAT (orders with tax_amount > 0 but sales_tax_amount = 0), grouped by tax rate'
          items:
            $ref: '#/components/schemas/VatObligation'
    VatObligation:
      type: object
      description: VAT obligation information grouped by tax rate
      required:
        - amount
        - taxAmount
        - taxRate
      properties:
        amount:
          type: number
          format: double
          description: Base amount subject to VAT
          minimum: 0
          example: 50000
        taxAmount:
          type: number
          format: double
          description: VAT amount MP must pay
          minimum: 0
          example: 4000
        taxRate:
          type: number
          format: double
          description: VAT rate percentage
          minimum: 0
          maximum: 100
          example: 8
    PlatformFees:
      type: object
      description: Platform fees data
      required:
        - memberDiscount
        - sellingFees
        - buyingFees
      properties:
        memberDiscount:
          type: number
          format: double
          description: Total member discounts applied
          minimum: 0
          example: 5000
        sellingFees:
          type: number
          format: double
          description: Selling fees including taxes
          minimum: 0
          example: 12500
        buyingFees:
          type: number
          format: double
          description: Buying fees including taxes
          minimum: 0
          example: 8750
    CountryBreakdown:
      type: object
      description: Country breakdown statistics
      required:
        - countryCode
        - sellerRevenue
        - marketplaceVAT
        - platformFees
      properties:
        countryCode:
          type: string
          description: ISO3 country code
          pattern: '^[A-Z]{3}$'
          example: USA
        displayName:
          type: object
          additionalProperties:
            type: string
          description: Country display name
          example:
            en: United States
        sellerRevenue:
          $ref: '#/components/schemas/SellerRevenue'
        marketplaceVAT:
          $ref: '#/components/schemas/MarketplaceVAT'
        platformFees:
          $ref: '#/components/schemas/PlatformFees'
    RegionBreakdown:
      type: object
      description: Region breakdown statistics
      required:
        - regionId
        - regionName
        - sellerRevenue
        - marketplaceVAT
        - platformFees
      properties:
        regionId:
          type: string
          description: Region ID
          example: region_001
        displayName:
          type: object
          additionalProperties:
            type: string
          description: Region display name
          example:
            en: United States
        sellerRevenue:
          $ref: '#/components/schemas/SellerRevenue'
        marketplaceVAT:
          $ref: '#/components/schemas/MarketplaceVAT'
        platformFees:
          $ref: '#/components/schemas/PlatformFees'
    OrderStatisticsResponse:
      type: object
      description: Order statistics response object
      required:
        - data
      properties:
        data:
          $ref: '#/components/schemas/OrderStatistics'
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
