// Code generated by mockery v2.43.0. DO NOT EDIT.

package cms

import (
	internalcms "as-api/as/internal/cms"
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// CmsDomain is an autogenerated mock type for the CmsDomain type
type CmsDomain struct {
	mock.Mock
}

// CreateContent provides a mock function with given fields: ctx, content
func (_m *CmsDomain) CreateContent(ctx context.Context, content *internalcms.Content) (*internalcms.Content, error) {
	ret := _m.Called(ctx, content)

	if len(ret) == 0 {
		panic("no return value specified for CreateContent")
	}

	var r0 *internalcms.Content
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internalcms.Content) (*internalcms.Content, error)); ok {
		return rf(ctx, content)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internalcms.Content) *internalcms.Content); ok {
		r0 = rf(ctx, content)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalcms.Content)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internalcms.Content) error); ok {
		r1 = rf(ctx, content)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateNotification provides a mock function with given fields: ctx, req
func (_m *CmsDomain) CreateNotification(ctx context.Context, req *internalcms.Notification) (*internalcms.Notification, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for CreateNotification")
	}

	var r0 *internalcms.Notification
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internalcms.Notification) (*internalcms.Notification, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internalcms.Notification) *internalcms.Notification); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalcms.Notification)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internalcms.Notification) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateUserNotification provides a mock function with given fields: ctx, userID, notificationID
func (_m *CmsDomain) CreateUserNotification(ctx context.Context, userID string, notificationID string) error {
	ret := _m.Called(ctx, userID, notificationID)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserNotification")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) error); ok {
		r0 = rf(ctx, userID, notificationID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateUserNotificationsBatch provides a mock function with given fields: ctx, userIDs, notificationID
func (_m *CmsDomain) CreateUserNotificationsBatch(ctx context.Context, userIDs []string, notificationID string) error {
	ret := _m.Called(ctx, userIDs, notificationID)

	if len(ret) == 0 {
		panic("no return value specified for CreateUserNotificationsBatch")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []string, string) error); ok {
		r0 = rf(ctx, userIDs, notificationID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteContent provides a mock function with given fields: ctx, id
func (_m *CmsDomain) DeleteContent(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteContent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// DeleteNotification provides a mock function with given fields: ctx, id
func (_m *CmsDomain) DeleteNotification(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteNotification")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string) error); ok {
		r0 = rf(ctx, id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GetContentByID provides a mock function with given fields: ctx, id
func (_m *CmsDomain) GetContentByID(ctx context.Context, id string) (*internalcms.Content, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetContentByID")
	}

	var r0 *internalcms.Content
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*internalcms.Content, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *internalcms.Content); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalcms.Content)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContentCategories provides a mock function with given fields: ctx
func (_m *CmsDomain) GetContentCategories(ctx context.Context) ([]*internalcms.ContentCategory, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetContentCategories")
	}

	var r0 []*internalcms.ContentCategory
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*internalcms.ContentCategory, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*internalcms.ContentCategory); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internalcms.ContentCategory)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetContents provides a mock function with given fields: ctx, filter
func (_m *CmsDomain) GetContents(ctx context.Context, filter *internalcms.ContentFilter) ([]*internalcms.Content, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetContents")
	}

	var r0 []*internalcms.Content
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internalcms.ContentFilter) ([]*internalcms.Content, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internalcms.ContentFilter) []*internalcms.Content); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internalcms.Content)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internalcms.ContentFilter) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCountries provides a mock function with given fields: ctx, filter
func (_m *CmsDomain) GetCountries(ctx context.Context, filter *internalcms.CountryFilter) ([]*internalcms.Country, error) {
	ret := _m.Called(ctx, filter)

	if len(ret) == 0 {
		panic("no return value specified for GetCountries")
	}

	var r0 []*internalcms.Country
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *internalcms.CountryFilter) ([]*internalcms.Country, error)); ok {
		return rf(ctx, filter)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *internalcms.CountryFilter) []*internalcms.Country); ok {
		r0 = rf(ctx, filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internalcms.Country)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *internalcms.CountryFilter) error); ok {
		r1 = rf(ctx, filter)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetCountryByCodes provides a mock function with given fields: ctx, codes
func (_m *CmsDomain) GetCountryByCodes(ctx context.Context, codes ...string) ([]*internalcms.Country, error) {
	_va := make([]interface{}, len(codes))
	for _i := range codes {
		_va[_i] = codes[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetCountryByCodes")
	}

	var r0 []*internalcms.Country
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...string) ([]*internalcms.Country, error)); ok {
		return rf(ctx, codes...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...string) []*internalcms.Country); ok {
		r0 = rf(ctx, codes...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internalcms.Country)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...string) error); ok {
		r1 = rf(ctx, codes...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetFutureNotificationsReadyToPublish provides a mock function with given fields: ctx
func (_m *CmsDomain) GetFutureNotificationsReadyToPublish(ctx context.Context) ([]*internalcms.Notification, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFutureNotificationsReadyToPublish")
	}

	var r0 []*internalcms.Notification
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*internalcms.Notification, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*internalcms.Notification); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internalcms.Notification)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetLanguages provides a mock function with given fields: ctx
func (_m *CmsDomain) GetLanguages(ctx context.Context) ([]*internalcms.Language, error) {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetLanguages")
	}

	var r0 []*internalcms.Language
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context) ([]*internalcms.Language, error)); ok {
		return rf(ctx)
	}
	if rf, ok := ret.Get(0).(func(context.Context) []*internalcms.Language); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internalcms.Language)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context) error); ok {
		r1 = rf(ctx)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetNotificationById provides a mock function with given fields: ctx, id
func (_m *CmsDomain) GetNotificationById(ctx context.Context, id string) (*internalcms.Notification, error) {
	ret := _m.Called(ctx, id)

	if len(ret) == 0 {
		panic("no return value specified for GetNotificationById")
	}

	var r0 *internalcms.Notification
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*internalcms.Notification, error)); ok {
		return rf(ctx, id)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *internalcms.Notification); ok {
		r0 = rf(ctx, id)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalcms.Notification)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, id)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetNotifications provides a mock function with given fields: ctx, params
func (_m *CmsDomain) GetNotifications(ctx context.Context, params internalcms.GetNotificationsParams) (*internalcms.GetNotificationsResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetNotifications")
	}

	var r0 *internalcms.GetNotificationsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, internalcms.GetNotificationsParams) (*internalcms.GetNotificationsResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, internalcms.GetNotificationsParams) *internalcms.GetNotificationsResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalcms.GetNotificationsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, internalcms.GetNotificationsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetPublishedNoticesByCountry provides a mock function with given fields: ctx, countryCode
func (_m *CmsDomain) GetPublishedNoticesByCountry(ctx context.Context, countryCode string) (*internalcms.GetNotificationsResponse, error) {
	ret := _m.Called(ctx, countryCode)

	if len(ret) == 0 {
		panic("no return value specified for GetPublishedNoticesByCountry")
	}

	var r0 *internalcms.GetNotificationsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (*internalcms.GetNotificationsResponse, error)); ok {
		return rf(ctx, countryCode)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) *internalcms.GetNotificationsResponse); ok {
		r0 = rf(ctx, countryCode)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalcms.GetNotificationsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, countryCode)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetRegionsByIds provides a mock function with given fields: ctx, ids
func (_m *CmsDomain) GetRegionsByIds(ctx context.Context, ids ...string) ([]*internalcms.Region, error) {
	_va := make([]interface{}, len(ids))
	for _i := range ids {
		_va[_i] = ids[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for GetRegionsByIds")
	}

	var r0 []*internalcms.Region
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, ...string) ([]*internalcms.Region, error)); ok {
		return rf(ctx, ids...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, ...string) []*internalcms.Region); ok {
		r0 = rf(ctx, ids...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*internalcms.Region)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, ...string) error); ok {
		r1 = rf(ctx, ids...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// IsCountryDeactivated provides a mock function with given fields: ctx, code
func (_m *CmsDomain) IsCountryDeactivated(ctx context.Context, code string) (bool, error) {
	ret := _m.Called(ctx, code)

	if len(ret) == 0 {
		panic("no return value specified for IsCountryDeactivated")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (bool, error)); ok {
		return rf(ctx, code)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) bool); ok {
		r0 = rf(ctx, code)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, code)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateContent provides a mock function with given fields: ctx, id, content
func (_m *CmsDomain) UpdateContent(ctx context.Context, id string, content *internalcms.Content) error {
	ret := _m.Called(ctx, id, content)

	if len(ret) == 0 {
		panic("no return value specified for UpdateContent")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *internalcms.Content) error); ok {
		r0 = rf(ctx, id, content)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateNotification provides a mock function with given fields: ctx, id, req
func (_m *CmsDomain) UpdateNotification(ctx context.Context, id string, req *internalcms.Notification) (*internalcms.Notification, error) {
	ret := _m.Called(ctx, id, req)

	if len(ret) == 0 {
		panic("no return value specified for UpdateNotification")
	}

	var r0 *internalcms.Notification
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *internalcms.Notification) (*internalcms.Notification, error)); ok {
		return rf(ctx, id, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *internalcms.Notification) *internalcms.Notification); ok {
		r0 = rf(ctx, id, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*internalcms.Notification)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *internalcms.Notification) error); ok {
		r1 = rf(ctx, id, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewCmsDomain creates a new instance of CmsDomain. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewCmsDomain(t interface {
	mock.TestingT
	Cleanup(func())
}) *CmsDomain {
	mock := &CmsDomain{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
