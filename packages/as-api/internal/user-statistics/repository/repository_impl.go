package repository

import (
	"context"

	"as-api/as/foundations/db/dao"
	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/db/factory"
	"as-api/as/internal/user-statistics/model"
	"as-api/as/pkg/time"

	"github.com/pkg/errors"
)

type userStatisticsRepository struct {
	db *factory.DO
}

// NewUserStatisticsRepository creates a new user statistics repository instance
func NewUserStatisticsRepository(db *factory.DO) UserStatisticsRepository {
	return &userStatisticsRepository{
		db: db,
	}
}

// UpsertUserStatistics stores or updates user statistics
func (r *userStatisticsRepository) UpsertUserStatistics(ctx context.Context, country string, statistics []*entities.UserStatistic) error {
	db := r.db.Model(nil)

	if len(statistics) == 0 {
		return nil
	}

	// Use transaction for batch upsert
	return db.Query.Transaction(func(tx *dao.Query) error {
		usq := tx.UserStatistic

		q := usq.WithContext(ctx)

		if _, err := q.Where(usq.Country.Eq(country)).Delete(); err != nil {
			return errors.Wrap(err, "delete user statistics")
		}

		if err := q.Create(statistics...); err != nil {
			return errors.Wrap(err, "upsert user statistics")
		}

		return nil
	})
}

// GetUserStatistics retrieves user statistics with filters
func (r *userStatisticsRepository) GetUserStatistics(ctx context.Context, filter *UserStatisticsFilter) ([]*entities.UserStatistic, error) {
	db := r.db.Model(nil)
	usq := db.Query.UserStatistic
	regionQ := db.Query.Region
	query := usq.WithContext(ctx)

	query = query.LeftJoin(regionQ, regionQ.ID.EqCol(usq.RegionID))

	// Apply filters
	if filter.Country != nil {
		query = query.Where(usq.Country.Eq(*filter.Country))
	}
	if filter.RegionID != nil {
		query = query.Where(regionQ.Where(regionQ.ParentID.Eq(*filter.RegionID)).Or(regionQ.ID.Eq(*filter.RegionID)))
	} else {
		query = query.Where(regionQ.ParentID.IsNull())
	}
	if filter.UserType != nil {
		query = query.Where(usq.UserType.Eq(*filter.UserType))
	}

	statistics, err := query.Find()
	if err != nil {
		return nil, errors.Wrap(err, "find user statistics")
	}

	return statistics, nil
}

func (r *userStatisticsRepository) GetAllCountries(ctx context.Context) ([]string, error) {
	db := r.db.Model(nil)
	countryQ := db.Query.Country
	query := countryQ.WithContext(ctx)

	query = query.Select(countryQ.Code).Where(countryQ.Active)

	var results []string
	if err := query.Scan(&results); err != nil {
		return nil, errors.Wrap(err, "get all countries")
	}

	return results, nil
}

func (r *userStatisticsRepository) GetAllRegionsByCountry(ctx context.Context, country string) ([]*entities.Region, error) {
	db := r.db.Model(nil)
	regionQ := db.Query.Region
	query := regionQ.WithContext(ctx)

	query = query.Select(regionQ.ID, regionQ.ParentID)
	query = query.Where(regionQ.CountryCode.Eq(country))

	regions, err := query.Find()
	if err != nil {
		return nil, errors.Wrap(err, "get all regions")
	}

	return regions, nil
}

func (r *userStatisticsRepository) GetAllUserByCountry(ctx context.Context, country string) ([]*model.User, error) {
	db := r.db.Model(&model.User{})
	userQ := db.Query.User
	uftq := db.Query.UserFcmToken

	rawQuery := db.UnderlyingDB().
		Distinct("users.id", "home_address->>'regionId' as region_id", "user_fcm_tokens.id is not null as active", "seller_id", "membership")
	db.ReplaceDB(rawQuery)

	activeThreshold := time.TodayInDefaultTZ().AddDate(0, 0, -30)
	q := db.WithContext(ctx).
		LeftJoin(uftq, uftq.UserID.EqCol(userQ.ID), uftq.LastUsedAt.Gte(activeThreshold)).
		Where(userQ.CountryCode.Eq(country), userQ.DeletedAt.IsNull())

	users, err := q.Find()
	if err != nil {
		return nil, errors.Wrap(err, "get all users by country")
	}

	return users.([]*model.User), nil
}
