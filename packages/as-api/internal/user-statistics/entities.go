package userstatistics

import (
	"time"
)

// Domain entities - these represent business concepts and should not expose internal model details

// UserStatistics represents user statistics business entity
type UserStatistics struct {
	ID                   *string   `json:"id"`
	Country              string    `json:"country"`
	RegionID             *string   `json:"region_id"`
	UserType             string    `json:"user_type"`
	TotalCount           int       `json:"total_count"`
	ActiveCount          int       `json:"active_count"`
	CalculationTimestamp time.Time `json:"calculation_timestamp"`
	CalculationDate      time.Time `json:"calculation_date"`
	CreatedAt            time.Time `json:"created_at"`
	UpdatedAt            time.Time `json:"updated_at"`
}

// UserStatisticsFilter represents filter parameters for querying user statistics
type UserStatisticsFilter struct {
	Country         *string
	RegionID        *string
	UserType        *string
	StartDate       *time.Time
	EndDate         *time.Time
	CalculationDate *time.Time
	Page            *int
	Limit           *int
}

// StatisticsSummary represents aggregated statistics summary
type StatisticsSummary struct {
	TotalUsers   int `json:"total_users"`
	ActiveUsers  int `json:"active_users"`
	SellerUsers  int `json:"seller_users"`
	PremiumUsers int `json:"premium_users"`
	GeneralUsers int `json:"general_users"`

	// Breakdown by country
	CountryBreakdown []*CountryStatistics `json:"country_breakdown,omitempty"`

	// Breakdown by region
	RegionBreakdown []*RegionStatistics `json:"region_breakdown,omitempty"`
}

// CountryStatistics represents statistics for a specific country
type CountryStatistics struct {
	Country      string `json:"country"`
	TotalUsers   int    `json:"total_users"`
	ActiveUsers  int    `json:"active_users"`
	SellerUsers  int    `json:"seller_users"`
	PremiumUsers int    `json:"premium_users"`
	GeneralUsers int    `json:"general_users"`
}

// RegionStatistics represents statistics for a specific region
type RegionStatistics struct {
	RegionID     string `json:"region_id"`
	TotalUsers   int    `json:"total_users"`
	ActiveUsers  int    `json:"active_users"`
	SellerUsers  int    `json:"seller_users"`
	PremiumUsers int    `json:"premium_users"`
	GeneralUsers int    `json:"general_users"`
}

// UserTypeStatistics represents statistics for a specific user type
type UserTypeStatistics struct {
	UserType    string `json:"user_type"`
	TotalUsers  int    `json:"total_users"`
	ActiveUsers int    `json:"active_users"`
}

// UserCountData represents raw user count data for calculation
type UserCountData struct {
	UserID     string     `gorm:"column:user_id"`
	Country    string     `gorm:"column:country"`
	RegionID   string     `gorm:"column:region_id"`
	SellerID   *string    `gorm:"column:seller_id"`
	Membership *int32     `gorm:"column:membership"`
	LastUsedAt *time.Time `gorm:"column:last_used_at"`
	IsActive   bool       `gorm:"column:is_active"`
}

// CalculationParams represents parameters for user statistics calculation
type CalculationParams struct {
	Country         string
	RegionID        string
	UserType        string
	CalculationDate time.Time
}

type Address struct {
	RegionId *string `json:"regionId,omitempty"`
}
