package userstatistics

import (
	"log"

	"as-api/as/internal/user-statistics/repository"
	"as-api/as/pkg/di"
)

func init() {
	if err := di.RegisterProviders(NewUserStatisticsDomain); err != nil {
		log.Fatal("register constructor user statistics domain failed:", err)
	}

	if err := di.RegisterProviders(repository.NewUserStatisticsRepository); err != nil {
		log.Fatal("register constructor user statistics repository failed:", err)
	}
}
