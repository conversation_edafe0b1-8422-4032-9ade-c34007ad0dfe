package repository

import (
	"context"
	"log"

	"as-api/as/foundations/db/dao"
	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/db/factory"
	"as-api/as/internal/cms/model"
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/apiutil"
	"as-api/as/pkg/helpers/pointer"
	pkgtime "as-api/as/pkg/time"

	"github.com/pkg/errors"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func init() {
	if err := di.RegisterProviders(NewCmsRepository); err != nil {
		log.Fatal("register constructor cms repository failed:", err)
	}
}

type CmsRepository interface {
	GetLanguages(ctx context.Context) ([]*model.Language, error)
	GetCountries(ctx context.Context, filter map[string]any) ([]*model.Country, error)
	GetCountryByCode(ctx context.Context, code string) (*model.Country, error)
	GetCountryByCodes(ctx context.Context, codes ...string) ([]*model.Country, error)
	GetRegionsByIds(ctx context.Context, ids ...string) ([]*entities.Region, error)
	GetContentCategories(ctx context.Context) ([]*model.ContentCategory, error)
	GetContents(ctx context.Context, filter map[string]interface{}, page *int, limit *int) ([]*model.Content, error)
	GetContentByID(ctx context.Context, id string) (*model.Content, error)
	CreateContent(ctx context.Context, content *entities.Content) (*entities.Content, error)
	UpdateContent(ctx context.Context, id string, content *entities.Content) (*entities.Content, error)
	DeleteContent(ctx context.Context, id string) error

	// Notification methods
	GetNotifications(ctx context.Context, filter map[string]interface{}, page int, limit int) ([]*entities.Notification, *int, error)
	GetNotificationById(ctx context.Context, id string) (*entities.Notification, error)
	GetNotificationByGroup(ctx context.Context, groupID string) (*entities.Notification, error)
	CreateNotification(ctx context.Context, notification *entities.Notification) (*entities.Notification, error)
	DeleteNotification(ctx context.Context, id string) error
	UpdateNotification(ctx context.Context, id string, notification *entities.Notification) (*entities.Notification, error)
	UpsertNotificationByGroup(ctx context.Context, notification *entities.Notification) (*entities.Notification, error)
	CreateUserNotification(ctx context.Context, userID, notificationID string) error
	CreateUserNotificationsBatch(ctx context.Context, userIDs []string, notificationID string) error

	// Future notification methods
	GetFutureNotificationsReadyToPublish(ctx context.Context) ([]*entities.Notification, error)
}

type cmsRepository struct {
	db *factory.DO
}

// GetLanguages implements CmsRepository.
func (r *cmsRepository) GetLanguages(ctx context.Context) ([]*model.Language, error) {
	db := r.db.Model(&model.Language{})
	l := db.Query.Language

	langs, err := db.WithContext(ctx).Preload(field.Associations).Order(l.DisplayOrder).Find()
	if err != nil {
		return nil, errors.Wrap(err, "get languages")
	}

	return langs.([]*model.Language), nil
}

func (r *cmsRepository) GetCountries(ctx context.Context, filter map[string]any) ([]*model.Country, error) {
	db := r.db.Model(&model.Country{})
	c := db.Query.Country

	q := db.WithContext(ctx).Order(c.DisplayOrder)

	if filter["is_minimal"] == true {
		q = q.Select(c.Code, c.DisplayName)
	} else {
		q = q.Preload(field.Associations)
	}

	if filter["has_include_inactive"] != true {
		q = q.Where(c.Active)
	}

	if filter["only_inactive"] == true {
		q = q.Where(c.Active.Is(false))
	}

	if countryCodes, _ := filter["country_codes"].([]string); len(countryCodes) > 0 {
		q = q.Where(c.Code.In(countryCodes...))
	}

	countries, err := q.Find()
	if err != nil {
		return nil, errors.Wrap(err, "get countries")
	}

	return countries.([]*model.Country), nil
}

// GetCountryByCode implements CmsRepository.
func (r *cmsRepository) GetCountryByCode(ctx context.Context, code string) (*model.Country, error) {
	db := r.db.Model(&model.Country{})
	c := db.Query.Country

	country, err := db.WithContext(ctx).Preload(field.Associations).Where(c.Code.Eq(code)).First()
	if err != nil {
		return nil, errors.Wrap(err, "get country by code")
	}

	return country.(*model.Country), nil
}

func (r *cmsRepository) GetRegionsByIds(ctx context.Context, ids ...string) ([]*entities.Region, error) {
	db := r.db.Model(nil)
	rq := db.Query.Region
	q := rq.WithContext(ctx)

	if len(ids) > 0 {
		q = q.Where(rq.ID.In(ids...))
	}

	regions, err := q.Find()
	if err != nil {
		return nil, errors.Wrap(err, "get regions")
	}

	return regions, nil
}

func (r *cmsRepository) GetCountryByCodes(ctx context.Context, codes ...string) ([]*model.Country, error) {
	db := r.db.Model(&model.Country{})
	c := db.Query.Country

	q := db.WithContext(ctx)

	if len(codes) > 0 {
		q = q.Where(c.Code.In(codes...))
	}

	countries, err := q.Find()
	if err != nil {
		return nil, errors.Wrap(err, "get countries")
	}

	return countries.([]*model.Country), nil
}

// CreateContent implements CmsRepository.
func (r *cmsRepository) CreateContent(ctx context.Context, content *entities.Content) (*entities.Content, error) {
	db := r.db.Model(nil)
	c := db.Query.Content

	if err := c.WithContext(ctx).Create(content); err != nil {
		return nil, errors.Wrap(err, "create content")
	}

	return content, nil
}

// DeleteContent implements CmsRepository.
func (r *cmsRepository) DeleteContent(ctx context.Context, id string) error {
	db := r.db.Model(nil)
	c := db.Query.Content

	if _, err := c.WithContext(ctx).Where(c.ID.Eq(id)).Delete(); err != nil {
		return errors.Wrap(err, "delete content")
	}

	return nil
}

// GetContentCategories implements CmsRepository.
func (r *cmsRepository) GetContentCategories(ctx context.Context) ([]*model.ContentCategory, error) {
	db := r.db.Model(&model.ContentCategory{})
	cc := db.Query.ContentCategory

	ccs, err := db.WithContext(ctx).Preload(field.Associations).Order(cc.DisplayOrder).Find()
	if err != nil {
		return nil, errors.Wrap(err, "get content categories")
	}

	return ccs.([]*model.ContentCategory), nil
}

// GetContentsByCategoryCode implements CmsRepository.
func (r *cmsRepository) GetContents(ctx context.Context, filter map[string]interface{}, page *int, limit *int) ([]*model.Content, error) {
	db := r.db.Model(&model.Content{})
	c := db.Query.Content
	q := db.WithContext(ctx)

	for k, v := range filter {
		switch k {
		case "content_category_code":
			v, _ := v.(string)
			if v == "" {
				continue
			}
			q = q.Where(c.ContentCategoryCode.Eq(v))
		case "content_category_code_in":
			v, _ := v.([]string)
			if len(v) == 0 {
				continue
			}
			q = q.Where(c.ContentCategoryCode.In(v...))
		case "parent_content_id":
			v, _ := v.(string)
			if v == "" {
				continue
			}
			q = q.Where(c.ParentContentID.Eq(v))
		case "country_code":
			v, _ := v.(string)
			if v == "" {
				continue
			}
			q = q.Where(c.CountryCode.Eq(v))
		}
	}

	if filter["parent_content_id_is_null"] == true {
		q = q.Where(c.ParentContentID.IsNull())
	}

	if filter["is_published"] == true {
		q = q.Where(c.Status.Eq("published"), c.PublishedAt.Lte(pkgtime.TodayInDefaultTZ()))
	}

	if limit != nil {
		q = q.Limit(*limit)
		if page != nil {
			q = q.Offset((*page - 1) * *limit)
		}
	}

	contents, err := q.Preload(field.Associations).Find()
	if err != nil {
		return nil, errors.Wrap(err, "get contents")
	}

	return contents.([]*model.Content), nil
}

// GetContentByID implements CmsRepository.
func (r *cmsRepository) GetContentByID(ctx context.Context, id string) (*model.Content, error) {
	db := r.db.Model(&model.Content{})
	c := db.Query.Content

	content, err := db.WithContext(ctx).Preload(field.Associations).Where(c.ID.Eq(id)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.Wrap(apiutil.ErrResourceNotFound, "get content by id")
		}
		return nil, errors.Wrap(err, "get content by id")
	}

	return content.(*model.Content), nil
}

// UpdateContent implements CmsRepository.
func (r *cmsRepository) UpdateContent(ctx context.Context, id string, content *entities.Content) (*entities.Content, error) {
	db := r.db.Model(nil)
	c := db.Query.Content

	if _, err := c.WithContext(ctx).Where(c.ID.Eq(id)).Updates(content); err != nil {
		return nil, errors.Wrap(err, "update content")
	}

	return content, nil
}

// UpdateLanguage implements CmsRepository.
func (r *cmsRepository) UpdateLanguage(ctx context.Context, language *entities.Language) (*entities.Language, error) {
	db := r.db.Model(nil)
	l := db.Query.Language

	if _, err := l.WithContext(ctx).Where(l.Code.Eq(language.Code)).Updates(language); err != nil {
		return nil, errors.Wrap(err, "update language")
	}

	return language, nil
}

// GetNotifications implements CmsRepository
func (r *cmsRepository) GetNotifications(ctx context.Context, filter map[string]interface{}, page int, limit int) ([]*entities.Notification, *int, error) {
	// Get notifications with pagination
	db := r.db.Model(nil)
	nq := db.Query.Notification
	q := nq.WithContext(ctx)

	for k, v := range filter {
		switch k {
		case "system_type":
			v, _ := v.(string)
			if v == "" {
				continue
			}
			q = q.Where(nq.SystemType.Eq(v))
		case "status":
			status, _ := v.(string)
			if status == "" {
				continue
			}
			q = q.Where(nq.Status.Eq(status))
		case "types":
			types, _ := v.([]string)
			if len(types) == 0 {
				continue
			}
			q = q.Where(nq.Type.In(types...))
		case "country_code":
			code, _ := v.(string)
			if code == "" {
				continue
			}
			// Use raw DB to apply array contains and then replace back into gen DO
			rawDB := q.UnderlyingDB().Where("? = ANY(country_codes)", code)
			q.ReplaceDB(rawDB)
		case "published_at_lte_today":
			// Ensure published_at is not null and <= today in DefaultTZ
			if ok, _ := v.(bool); ok {
				today := pkgtime.TodayInDefaultTZ()
				q = q.Where(nq.PublishedAt.IsNotNull()).Where(nq.PublishedAt.Lte(today))
			}
		}
	}

	// Filter out user-specific notifications (those without country_codes)
	// Only get notifications that have country_codes (general notifications)
	q = q.Where(nq.CountryCodes.IsNotNull())

	// Count total records
	count, err := q.Count()
	if err != nil {
		return nil, nil, errors.Wrap(err, "count notifications")
	}
	totalInt := int(count)

	// Apply pagination if needed
	if limit > 0 {
		q = q.Limit(limit)
		if page > 0 {
			q = q.Offset((page - 1) * limit)
		}
	}

	// Get notifications
	result, err := q.Order(nq.CreatedAt.Desc()).Find()
	if err != nil {
		return nil, nil, errors.Wrap(err, "get notifications")
	}

	return result, &totalInt, nil
}

func (r *cmsRepository) GetNotificationById(ctx context.Context, id string) (*entities.Notification, error) {
	db := r.db.Model(nil)
	nq := db.Query.Notification

	notification, err := nq.WithContext(ctx).Where(nq.ID.Eq(id)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.Wrap(apiutil.ErrResourceNotFound, "get notification by id")
		}
		return nil, errors.Wrap(err, "get notification by id")
	}

	return notification, nil
}

// GetNotificationByGroup implements CmsRepository
func (r *cmsRepository) GetNotificationByGroup(ctx context.Context, groupID string) (*entities.Notification, error) {
	db := r.db.Model(nil)
	nq := db.Query.Notification

	notification, err := nq.WithContext(ctx).Where(nq.GroupID.Eq(groupID)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.Wrap(apiutil.ErrResourceNotFound, "get notification by group")
		}
		return nil, errors.Wrap(err, "get notification by group")
	}

	return notification, nil
}

// CreateNotification implements CmsRepository
func (r *cmsRepository) CreateNotification(ctx context.Context, notification *entities.Notification) (*entities.Notification, error) {
	db := r.db.Model(nil)
	nq := db.Query.Notification

	if err := nq.WithContext(ctx).Create(notification); err != nil {
		return nil, errors.Wrap(err, "create notification")
	}

	return notification, nil
}

// DeleteNotification implements CmsRepository
func (r *cmsRepository) DeleteNotification(ctx context.Context, id string) error {
	db := r.db.Model(nil)

	if err := db.Query.Transaction(func(tx *dao.Query) error {
		unq := tx.UserNotification
		if _, err := unq.WithContext(ctx).Where(unq.NotificationID.Eq(id)).Delete(); err != nil {
			return errors.Wrap(err, "delete user notification")
		}

		nq := tx.Notification
		if _, err := nq.WithContext(ctx).Where(nq.ID.Eq(id)).Delete(); err != nil {
			return errors.Wrap(err, "delete notification")
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "transaction")
	}

	return nil
}

// UpdateNotification implements CmsRepository
func (r *cmsRepository) UpdateNotification(ctx context.Context, id string, notification *entities.Notification) (*entities.Notification, error) {
	db := r.db.Model(nil)
	nq := db.Query.Notification

	notification.ID = pointer.Ptr(id)
	if _, err := nq.WithContext(ctx).Where(nq.ID.Eq(id)).Updates(notification); err != nil {
		return nil, errors.Wrap(err, "update notification")
	}

	return notification, nil
}

// UpsertNotificationByGroup implements CmsRepository
func (r *cmsRepository) UpsertNotificationByGroup(ctx context.Context, notification *entities.Notification) (*entities.Notification, error) {
	if notification.GroupID == nil || *notification.GroupID == "" {
		// If no group ID, create normally
		return r.CreateNotification(ctx, notification)
	}

	db := r.db.Model(nil)
	nq := db.Query.Notification

	// Try to find existing notification by group
	existing, err := nq.WithContext(ctx).Where(nq.GroupID.Eq(*notification.GroupID)).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// No existing notification, create new one
			return r.CreateNotification(ctx, notification)
		}
		return nil, errors.Wrap(err, "find existing notification by group")
	}

	// Update existing notification
	if _, err := nq.WithContext(ctx).Where(nq.ID.Eq(*existing.ID)).Updates(notification); err != nil {
		return nil, errors.Wrap(err, "update notification by group")
	}

	// Return updated notification
	updated, err := nq.WithContext(ctx).Where(nq.ID.Eq(*existing.ID)).First()
	if err != nil {
		return nil, errors.Wrap(err, "get updated notification")
	}

	return updated, nil
}

// CreateUserNotification implements CmsRepository
func (r *cmsRepository) CreateUserNotification(ctx context.Context, userID, notificationID string) error {
	db := r.db.Model(nil)
	unq := db.Query.UserNotification

	userNotification := &entities.UserNotification{
		UserID:         userID,
		NotificationID: notificationID,
		IsRead:         pointer.Ptr(false),
		CreatedAt:      pointer.Ptr(pkgtime.Now()),
		UpdatedAt:      pointer.Ptr(pkgtime.Now()),
	}

	if err := unq.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "notification_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"is_read", "created_at", "updated_at"}),
	}).Create(userNotification); err != nil {
		return errors.Wrap(err, "create user notification")
	}

	return nil
}

// CreateUserNotificationsBatch implements CmsRepository
func (r *cmsRepository) CreateUserNotificationsBatch(ctx context.Context, userIDs []string, notificationID string) error {
	if len(userIDs) == 0 {
		return nil
	}

	db := r.db.Model(nil)
	unq := db.Query.UserNotification

	// Prepare batch of user notifications
	userNotifications := make([]*entities.UserNotification, 0, len(userIDs))
	for _, userID := range userIDs {
		userNotifications = append(userNotifications, &entities.UserNotification{
			UserID:         userID,
			NotificationID: notificationID,
			IsRead:         pointer.Ptr(false),
			EmailSent:      pointer.Ptr(false),
			CreatedAt:      pointer.Ptr(pkgtime.Now()),
			UpdatedAt:      pointer.Ptr(pkgtime.Now()),
		})
	}

	// Create all user notifications in a single batch operation
	if err := unq.WithContext(ctx).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "notification_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"is_read", "created_at", "updated_at", "email_sent"}),
	}).CreateInBatches(userNotifications, 100); err != nil {
		return errors.Wrap(err, "create user notifications batch")
	}

	return nil
}

// GetFutureNotificationsReadyToPublish implements CmsRepository
func (r *cmsRepository) GetFutureNotificationsReadyToPublish(ctx context.Context) ([]*entities.Notification, error) {
	db := r.db.Model(nil)
	nq := db.Query.Notification

	today := pkgtime.TodayInDefaultTZ()

	notifications, err := nq.WithContext(ctx).
		Where(nq.Status.Eq("published")).
		Where(nq.CountryCodes.IsNotNull()).
		Where(nq.PublishedAt.Eq(today)).
		Find()

	if err != nil {
		return nil, errors.Wrap(err, "get future notifications ready to publish")
	}

	return notifications, nil
}

func NewCmsRepository(db *factory.DO) CmsRepository {
	return &cmsRepository{db: db}
}
