package cms

import (
	"time"

	"as-api/as/internal/cms/repository"
	"as-api/as/pkg/context"
	"as-api/as/pkg/jwt"

	"github.com/pkg/errors"
)

type Language struct {
	Code        string            `json:"code"`
	DisplayName map[string]string `json:"display_name"`
	Active      *bool             `json:"active"`
}

type Admin struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type Field struct {
	Code        string            `json:"code"`
	DisplayName map[string]string `json:"display_name"`
	Type        string            `json:"type"`
	Required    bool              `json:"required"`
}

type ContentType struct {
	Code   string  `json:"code"`
	Fields []Field `json:"fields"`
}

type ContentCategory struct {
	Code        string            `json:"code"`
	DisplayName map[string]string `json:"display_name"`
	ContentType ContentType       `json:"content_type"`
}

type ContentContent struct {
	Active      *bool     `json:"active,omitempty"`
	Attachments *[]string `json:"attachments,omitempty"`
	Body        *string   `json:"body,omitempty"`
	Url         *string   `json:"url,omitempty"`
	Title       *string   `json:"title,omitempty"`
}

type Content struct {
	ID                  *string                    `json:"id"`
	Title               map[string]string          `json:"title"`
	Content             map[string]*ContentContent `json:"content"`
	CountryCode         *string                    `json:"country_code"`
	ContentCategoryCode string                     `json:"content_category_code"`
	ParentContentID     *string                    `json:"parent_content_id"`
	PublishedAt         *time.Time                 `json:"published_at"`
	Status              *string                    `json:"status"`
	CreatedBy           *Admin                     `json:"created_by"`
	CreatedAt           *time.Time                 `json:"created_at"`
	UpdatedBy           *Admin                     `json:"updated_by"`
	UpdatedAt           *time.Time                 `json:"updated_at"`
}

type ContentFilter struct {
	ContentCategoryCode   string
	ContentCategoryCodeIn []string
	ParentContentID       string
	ParentContentIDIsNull bool
	IsPublished           bool
	CountryCode           string
	Page                  *int
	Limit                 *int
}

type CountryFilter struct {
	HasIncludeInactive bool
	OnlyInactive       bool
	CountryCode        []string
	IsMinimal          bool
}

type CmsDomain interface {
	GetLanguages(ctx context.Context) ([]*Language, error)
	GetCountries(ctx context.Context, filter *CountryFilter) ([]*Country, error)
	GetCountryByCodes(ctx context.Context, codes ...string) ([]*Country, error)
	GetRegionsByIds(ctx context.Context, ids ...string) ([]*Region, error)
	IsCountryDeactivated(ctx context.Context, code string) (bool, error)
	GetContentCategories(ctx context.Context) ([]*ContentCategory, error)
	CreateContent(ctx context.Context, content *Content) (*Content, error)
	GetContents(ctx context.Context, filter *ContentFilter) ([]*Content, error)
	GetContentByID(ctx context.Context, id string) (*Content, error)
	UpdateContent(ctx context.Context, id string, content *Content) error
	DeleteContent(ctx context.Context, id string) error

	// Include NotificationDomain interface methods
	NotificationDomain
}

type cmsDomain struct {
	repo repository.CmsRepository
}

func NewCmsDomain(repo repository.CmsRepository) CmsDomain {
	return &cmsDomain{
		repo: repo,
	}
}

func (d *cmsDomain) GetLanguages(ctx context.Context) ([]*Language, error) {
	langs, err := d.repo.GetLanguages(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get languages")
	}

	results := make([]*Language, 0, len(langs))
	for _, lang := range langs {
		language, err := ParseLanguageFromModel(lang)
		if err != nil {
			return nil, errors.Wrap(err, "parse language from model")
		}
		results = append(results, language)
	}

	return results, nil
}

func (d *cmsDomain) GetContentCategories(ctx context.Context) ([]*ContentCategory, error) {
	ccs, err := d.repo.GetContentCategories(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get content categories")
	}

	contentCategories := make([]*ContentCategory, 0, len(ccs))
	for _, cc := range ccs {
		contentCategory, err := ParseContentCategoryFromModel(cc)
		if err != nil {
			return nil, errors.Wrap(err, "parse content category from model")
		}
		contentCategories = append(contentCategories, contentCategory)
	}

	return contentCategories, nil
}

func (d *cmsDomain) GetContents(ctx context.Context, filter *ContentFilter) ([]*Content, error) {
	contents, err := d.repo.GetContents(ctx, map[string]interface{}{
		"content_category_code":     filter.ContentCategoryCode,
		"content_category_code_in":  filter.ContentCategoryCodeIn,
		"country_code":              filter.CountryCode,
		"parent_content_id":         filter.ParentContentID,
		"parent_content_id_is_null": filter.ParentContentIDIsNull,
		"is_published":              filter.IsPublished,
	}, filter.Page, filter.Limit)
	if err != nil {
		return nil, errors.Wrap(err, "get contents")
	}

	if len(contents) == 0 {
		return nil, nil
	}

	results := make([]*Content, 0, len(contents))
	for _, content := range contents {
		content, err := ParseContentFromModel(content)
		if err != nil {
			return nil, errors.Wrap(err, "parse content from model")
		}
		results = append(results, content)
	}

	return results, nil
}

func (d *cmsDomain) GetContentByID(ctx context.Context, id string) (*Content, error) {
	content, err := d.repo.GetContentByID(ctx, id)
	if err != nil {
		return nil, errors.Wrap(err, "get content by id")
	}

	c, err := ParseContentFromModel(content)
	if err != nil {
		return nil, errors.Wrap(err, "parse content from model")
	}

	return c, nil
}

func (d *cmsDomain) CreateContent(ctx context.Context, content *Content) (*Content, error) {
	claims, err := context.GetClaims[jwt.CustomClaims](ctx)
	if err != nil {
		return nil, errors.Wrap(err, "get claims")
	}

	contentEntity, err := ParseContentToEntity(content, &claims.UID)
	if err != nil {
		return nil, errors.Wrap(err, "parse content to entity")
	}

	c, err := d.repo.CreateContent(ctx, contentEntity)
	if err != nil {
		return nil, errors.Wrap(err, "create content")
	}

	return &Content{
		ID: c.ID,
	}, nil
}

func (d *cmsDomain) UpdateContent(ctx context.Context, id string, content *Content) error {
	claims, err := context.GetClaims[jwt.CustomClaims](ctx)
	if err != nil {
		return errors.Wrap(err, "get claims")
	}

	content.ID = &id
	contentEntity, err := ParseContentToEntity(content, &claims.UID)
	if err != nil {
		return errors.Wrap(err, "parse content to entity")
	}

	if _, err = d.repo.UpdateContent(ctx, id, contentEntity); err != nil {
		return errors.Wrap(err, "update content")
	}

	return nil
}

func (d *cmsDomain) DeleteContent(ctx context.Context, id string) error {
	err := d.repo.DeleteContent(ctx, id)
	if err != nil {
		return errors.Wrap(err, "delete content")
	}

	return nil
}

// IsCountryDeactivated checks if a country is deactivated
func (d *cmsDomain) IsCountryDeactivated(ctx context.Context, code string) (bool, error) {
	country, err := d.repo.GetCountryByCode(ctx, code)
	if err != nil {
		return true, errors.Wrap(err, "get country by code")
	}

	if country == nil || country.Active == nil {
		return true, nil
	}

	return !*country.Active, nil
}
