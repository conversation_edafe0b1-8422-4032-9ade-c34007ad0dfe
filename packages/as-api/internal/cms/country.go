package cms

import (
	"as-api/as/pkg/context"

	"github.com/pkg/errors"
)

const (
	CountryCodeUSA = "USA"
)

type Currency struct {
	Code                  string `json:"code"`
	IsBankAccountCurrency bool   `json:"is_bank_account_currency"`
}

type Country struct {
	Code            string
	DisplayName     map[string]string
	Regions         []*Region
	PhoneCode       *string
	Currencies      []*Currency
	IsCorporateOnly bool
	BankAccountType *string
	Active          *bool
}

type Region struct {
	Id          string
	DisplayName map[string]string
	SubRegions  []*Region
}

func (d *cmsDomain) GetCountries(ctx context.Context, filter *CountryFilter) ([]*Country, error) {
	countries, err := d.repo.GetCountries(ctx, map[string]any{
		"has_include_inactive": filter.HasIncludeInactive,
		"only_inactive":        filter.OnlyInactive,
		"country_codes":        filter.CountryCode,
		"is_minimal":           filter.IsMinimal,
	})
	if err != nil {
		return nil, err
	}

	results := make([]*Country, 0, len(countries))
	for _, c := range countries {
		country, err := ParseCountryFromModel(c)
		if err != nil {
			return nil, errors.Wrap(err, "parse country from model")
		}
		results = append(results, country)
	}

	return results, nil
}

func (d *cmsDomain) GetCountryByCodes(ctx context.Context, codes ...string) ([]*Country, error) {
	countries, err := d.repo.GetCountryByCodes(ctx, codes...)
	if err != nil {
		return nil, errors.Wrap(err, "get country by code")
	}

	results := make([]*Country, 0, len(countries))
	for _, c := range countries {
		country, err := ParseCountryFromModel(c)
		if err != nil {
			return nil, errors.Wrap(err, "parse country from model")
		}
		results = append(results, country)
	}

	return results, nil
}

func (d *cmsDomain) GetRegionsByIds(ctx context.Context, ids ...string) ([]*Region, error) {
	regions, err := d.repo.GetRegionsByIds(ctx, ids...)
	if err != nil {
		return nil, errors.Wrap(err, "get regions by ids")
	}

	results := make([]*Region, 0, len(regions))
	for _, r := range regions {
		region, err := ParseRegionFromEntity(r)
		if err != nil {
			return nil, errors.Wrap(err, "parse region from entity")
		}
		results = append(results, region)
	}

	return results, nil
}
