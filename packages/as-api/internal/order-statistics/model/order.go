package model

import "as-api/as/foundations/db/entities"

type OrderDetail struct {
	entities.OrderDetail

	Order   *Order   `gorm:"foreignKey:OrderID" json:"order"`
	Product *Product `gorm:"foreignKey:ProductID" json:"product"`
}

type Product struct {
	entities.Product

	Seller *Seller `gorm:"foreignKey:SellerID" json:"seller"`
}

type Order struct {
	ID              *string        `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`
	ShippingAddress *entities.JSON `gorm:"column:shipping_address;type:jsonb" json:"shipping_address"`
}

type Seller struct {
	ID *string `gorm:"column:id;type:uuid;primaryKey;default:uuid_generate_v4()" json:"id"`

	User *User `gorm:"foreignKey:SellerID" json:"user"`
}
