package orderstatistics

import (
	"context"
	"log"
	"time"

	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/logger"
	"as-api/as/internal/order-statistics/model"
	"as-api/as/internal/order-statistics/repository"
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

func init() {
	if err := di.RegisterProviders(NewOrderStatisticsDomain); err != nil {
		log.Fatal("register constructor order statistics domain failed:", err)
	}
}

type OrderStatisticsDomain interface {
	CalculateAllSellersDailyStatistics(ctx context.Context, date time.Time) error
	CalculateAllSellersMonthlyStatistics(ctx context.Context, year, month int) error
}

type orderStatisticsDomain struct {
	orderStatisticsRepository repository.OrderStatisticsRepository
	logger                    logger.Logger
}

func NewOrderStatisticsDomain(orderStatisticsRepository repository.OrderStatisticsRepository, logger logger.Logger) OrderStatisticsDomain {
	return &orderStatisticsDomain{
		orderStatisticsRepository: orderStatisticsRepository,
		logger:                    logger,
	}
}

func (d *orderStatisticsDomain) CalculateAllSellersDailyStatistics(ctx context.Context, date time.Time) error {
	startDate := date.AddDate(0, 0, -1)
	endDate := date

	items, err := d.orderStatisticsRepository.GetOrdersCompletedByDate(ctx, startDate, endDate)
	if err != nil {
		return errors.Wrap(err, "get orders completed by date")
	}

	mRegionIds := make(map[string]bool)
	statsKeys := make([]*OrderStatisticsItem, 0, len(items))
	for _, item := range items {
		statsKey, err := convertOrderStatisticsItemFromModel(item)
		if err != nil {
			d.logger.Error("convert order statistics item to model", logger.FieldMap{
				"error": err,
			})
			continue
		}
		statsKeys = append(statsKeys, statsKey)
		if regionId := pointer.Safe(statsKey.SellerRegionID); regionId != "" {
			mRegionIds[regionId] = true
		}
		if regionId := pointer.Safe(statsKey.BuyerRegionID); regionId != "" {
			mRegionIds[regionId] = true
		}
	}

	regionIds := make([]string, 0, len(mRegionIds))
	for regionId := range mRegionIds {
		regionIds = append(regionIds, regionId)
	}

	regions, err := d.orderStatisticsRepository.GetRegionsByIDs(ctx, regionIds)
	if err != nil {
		return errors.Wrap(err, "get regions by ids")
	}

	mRegion := make(map[string]*model.Region)
	for _, region := range regions {
		mRegion[pointer.Safe(region.ID)] = region
	}

	mCountryStats := make(map[string]*OrderStatisticsDaily)
	mRegionStats := make(map[string]*OrderStatisticsDaily)
	for _, statsKey := range statsKeys {
		countryStats := mCountryStats[statsKey.SellerCountryCode]
		if countryStats == nil {
			countryStats = &OrderStatisticsDaily{
				Date:        date,
				CountryCode: &statsKey.SellerCountryCode,
			}
		}
		mCountryStats[statsKey.SellerCountryCode] = mergeSellerStats(countryStats, statsKey)

		regionId := pointer.Safe(statsKey.SellerRegionID)
		if regionId == "" {
			continue
		}
		region := mRegion[regionId]
		if region == nil {
			continue
		}

		for _, region := range region.Regions {
			regionId := pointer.Safe(region.ID)
			regionStats := mRegionStats[regionId]
			if regionStats == nil {
				regionStats = &OrderStatisticsDaily{
					Date:        date,
					CountryCode: &statsKey.SellerCountryCode,
					RegionID:    &regionId,
				}
			}
			mRegionStats[regionId] = mergeSellerStats(regionStats, statsKey)
		}
	}

	for _, statsKey := range statsKeys {
		countryStats := mCountryStats[statsKey.BuyerCountryCode]
		if countryStats == nil {
			countryStats = &OrderStatisticsDaily{
				Date:        date,
				CountryCode: &statsKey.BuyerCountryCode,
			}
		}
		mCountryStats[statsKey.BuyerCountryCode] = mergeBuyerStats(countryStats, statsKey, "")

		regionId := pointer.Safe(statsKey.BuyerRegionID)
		if regionId == "" {
			continue
		}
		region := mRegion[regionId]
		if region == nil {
			continue
		}

		for _, region := range region.Regions {
			regionId := pointer.Safe(region.ID)
			regionStats := mRegionStats[regionId]
			if regionStats == nil {
				regionStats = &OrderStatisticsDaily{
					Date:        date,
					CountryCode: &statsKey.BuyerCountryCode,
					RegionID:    &regionId,
				}
			}
			mRegionStats[regionId] = mergeBuyerStats(regionStats, statsKey, regionId)
		}
	}

	results := make([]*OrderStatisticsDaily, 0, len(mCountryStats)+len(mRegionStats))

	for _, countryStats := range mCountryStats {
		results = append(results, countryStats)
	}

	for _, regionStats := range mRegionStats {
		results = append(results, regionStats)
	}

	entities, err := convertSliceOrderStatisticsDailyToEntities(results)
	if err != nil {
		return errors.Wrap(err, "convert order statistics daily to entities")
	}

	if err = d.orderStatisticsRepository.UpsertOrderStatisticsDaily(ctx, date, entities); err != nil {
		return errors.Wrap(err, "upsert order statistics daily")
	}

	return nil
}

func mergeSellerStats(stats *OrderStatisticsDaily, statsKey *OrderStatisticsItem) *OrderStatisticsDaily {
	stats.OrderCount += 1
	stats.OrderAmount += statsKey.OrderAmount
	stats.SalesAmount += statsKey.SalesAmount
	stats.ProductPrice += statsKey.ProductPrice
	stats.MemberDiscountAmount += statsKey.MemberDiscountAmount
	stats.SalesFeesAmount += statsKey.SalesFeesAmount
	stats.PurchaseFeesAmount += statsKey.PurchaseFeesAmount
	return stats
}

func mergeBuyerStats(countryStats *OrderStatisticsDaily, statsKey *OrderStatisticsItem, regionId string) *OrderStatisticsDaily {
	taxInfo, taxConfig := foundTaxInfo(statsKey.TaxInfos, regionId)
	if taxInfo == nil {
		return countryStats
	}

	vatInfos := make([]*VatInfo, 0, len(taxInfo.Taxes))
	for _, tax := range taxInfo.Taxes {
		vatInfos = append(vatInfos, &VatInfo{
			Amount:    tax.Amount,
			TaxRate:   tax.TaxRate,
			TaxAmount: tax.TaxAmount,
		})
	}

	if taxConfig != nil && taxConfig.IsMarketplaceOperators {
		countryStats.VatMp = mergeVatInfos(vatInfos, countryStats.VatMp)
	} else {
		countryStats.VatSeller = mergeVatInfos(vatInfos, countryStats.VatSeller)
	}

	return countryStats
}

func foundTaxInfo(taxInfos []*TaxItemInfo, regionId string) (*TaxItemInfo, *TaxConfig) {
	for _, taxInfo := range taxInfos {
		if regionId == "" {
			if taxInfo.Region == nil && taxInfo.District == nil {
				return taxInfo, taxInfo.Country.TaxConfig
			}
			continue
		}

		if taxInfo.Region != nil && pointer.Safe(taxInfo.Region.ID) == regionId {
			return taxInfo, taxInfo.Region.TaxConfig
		}
	}
	return nil, nil
}

func mergeVatInfos(vatSrcs []*VatInfo, vatDsts []*VatInfo) []*VatInfo {
	for _, vat := range vatDsts {
		found := false
		for _, vatSrc := range vatSrcs {
			if vat.TaxRate == vatSrc.TaxRate {
				vat.Amount += vatSrc.Amount
				vat.TaxAmount += vatSrc.TaxAmount
				found = true
				break
			}
		}
		if !found {
			vatSrcs = append(vatSrcs, vat)
		}
	}
	return vatSrcs
}

func (d *orderStatisticsDomain) CalculateAllSellersMonthlyStatistics(ctx context.Context, year, month int) error {
	d.logger.Infof("Starting monthly order statistics aggregation for %d-%02d", year, month)

	// Get all daily statistics for the specified month
	dailyStats, err := d.orderStatisticsRepository.GetOrderStatisticsDailyByYearAndMonth(ctx, year, month)
	if err != nil {
		return errors.Wrapf(err, "get daily order statistics for %d-%02d", year, month)
	}

	if len(dailyStats) == 0 {
		d.logger.Warnf("No daily statistics found for %d-%02d", year, month)
		return nil
	}

	d.logger.Infof("Found %d daily statistics records for %d-%02d", len(dailyStats), year, month)

	// Aggregate daily statistics into monthly statistics
	monthlyStats := d.aggregateDailyToMonthly(dailyStats, year, month)

	d.logger.Infof("Aggregated into %d monthly statistics records", len(monthlyStats))

	// Store the monthly statistics
	if err := d.orderStatisticsRepository.UpsertOrderStatisticsMonthly(ctx, year, month, monthlyStats); err != nil {
		return errors.Wrapf(err, "upsert monthly order statistics for %d-%02d", year, month)
	}

	d.logger.Infof("Successfully stored monthly order statistics for %d-%02d", year, month)
	return nil
}

// aggregateDailyToMonthly aggregates daily statistics into monthly statistics
func (d *orderStatisticsDomain) aggregateDailyToMonthly(dailyStats []*entities.OrderStatisticsDaily, year, month int) []*entities.OrderStatisticsMonthly {
	// Map to store aggregated data by country and region
	statsMap := make(map[string]*entities.OrderStatisticsMonthly)

	for _, daily := range dailyStats {
		// Create a key for grouping by country and region
		key := d.createStatsKey(daily.CountryCode, daily.RegionID)

		// Get or create the monthly stats entry
		monthly, exists := statsMap[key]
		if !exists {
			monthly = &entities.OrderStatisticsMonthly{
				Year:        int32(year),
				Month:       int32(month),
				CountryCode: daily.CountryCode,
				RegionID:    daily.RegionID,
				// Initialize all numeric fields to zero
				OrderCount:           pointer.Ptr(int32(0)),
				OrderAmount:          pointer.Ptr(float64(0)),
				SalesAmount:          pointer.Ptr(float64(0)),
				ProductPrice:         pointer.Ptr(float64(0)),
				MemberDiscountAmount: pointer.Ptr(float64(0)),
				SalesFeesAmount:      pointer.Ptr(float64(0)),
				PurchaseFeesAmount:   pointer.Ptr(float64(0)),
			}
			statsMap[key] = monthly
		}

		// Aggregate the numeric values
		d.aggregateNumericValues(monthly, daily)

		// Aggregate VAT information
		d.aggregateVatInfo(monthly, daily)
	}

	// Convert map to slice
	result := make([]*entities.OrderStatisticsMonthly, 0, len(statsMap))
	for _, stats := range statsMap {
		result = append(result, stats)
	}

	return result
}

// createStatsKey creates a unique key for grouping statistics by country and region
func (d *orderStatisticsDomain) createStatsKey(countryCode *string, regionID *string) string {
	key := "global" // Default for records with no country/region

	if countryCode != nil && *countryCode != "" {
		key = *countryCode
		if regionID != nil && *regionID != "" {
			key = key + "_" + *regionID
		}
	}

	return key
}

// aggregateNumericValues adds daily numeric values to monthly totals
func (d *orderStatisticsDomain) aggregateNumericValues(monthly *entities.OrderStatisticsMonthly, daily *entities.OrderStatisticsDaily) {
	// Add order count
	if daily.OrderCount != nil {
		*monthly.OrderCount += *daily.OrderCount
	}

	// Add monetary amounts
	if daily.OrderAmount != nil {
		*monthly.OrderAmount += *daily.OrderAmount
	}
	if daily.SalesAmount != nil {
		*monthly.SalesAmount += *daily.SalesAmount
	}
	if daily.ProductPrice != nil {
		*monthly.ProductPrice += *daily.ProductPrice
	}
	if daily.MemberDiscountAmount != nil {
		*monthly.MemberDiscountAmount += *daily.MemberDiscountAmount
	}
	if daily.SalesFeesAmount != nil {
		*monthly.SalesFeesAmount += *daily.SalesFeesAmount
	}
	if daily.PurchaseFeesAmount != nil {
		*monthly.PurchaseFeesAmount += *daily.PurchaseFeesAmount
	}
}

// aggregateVatInfo aggregates VAT information from daily to monthly
func (d *orderStatisticsDomain) aggregateVatInfo(monthly *entities.OrderStatisticsMonthly, daily *entities.OrderStatisticsDaily) {
	// Aggregate VAT seller info
	if daily.VatSeller != nil {
		monthly.VatSeller = d.mergeVatJSON(monthly.VatSeller, daily.VatSeller)
	}

	// Aggregate VAT marketplace info
	if daily.VatMp != nil {
		monthly.VatMp = d.mergeVatJSON(monthly.VatMp, daily.VatMp)
	}
}

// mergeVatJSON merges VAT JSON data from daily into monthly
func (d *orderStatisticsDomain) mergeVatJSON(monthlyVat *entities.JSON, dailyVat *entities.JSON) *entities.JSON {
	if dailyVat == nil {
		return monthlyVat
	}

	// If monthly VAT is nil, initialize it
	if monthlyVat == nil {
		monthlyVat = &entities.JSON{}
	}

	// Parse daily VAT info
	dailyVatInfosPtr, err := entities.ConvertJSONToData[[]VatInfo](dailyVat)
	if err != nil {
		d.logger.Errorf("Failed to unmarshal daily VAT info: %v", err)
		return monthlyVat
	}
	var dailyVatInfos []VatInfo
	if dailyVatInfosPtr != nil {
		dailyVatInfos = *dailyVatInfosPtr
	}

	// Parse existing monthly VAT info
	var monthlyVatInfos []VatInfo
	if monthlyVat.String() != "null" && monthlyVat.String() != "" {
		monthlyVatInfosPtr, err := entities.ConvertJSONToData[[]VatInfo](monthlyVat)
		if err != nil {
			d.logger.Errorf("Failed to unmarshal monthly VAT info: %v", err)
			monthlyVatInfos = []VatInfo{}
		} else if monthlyVatInfosPtr != nil {
			monthlyVatInfos = *monthlyVatInfosPtr
		}
	}

	// Merge VAT infos by tax rate
	vatMap := make(map[float64]*VatInfo)

	// First, add existing monthly data to map
	for i := range monthlyVatInfos {
		vatInfo := &monthlyVatInfos[i]
		vatMap[vatInfo.TaxRate] = vatInfo
	}

	// Then, aggregate daily data
	for i := range dailyVatInfos {
		dailyVatInfo := &dailyVatInfos[i]
		if existingVat, ok := vatMap[dailyVatInfo.TaxRate]; ok {
			existingVat.Amount += dailyVatInfo.Amount
			existingVat.TaxAmount += dailyVatInfo.TaxAmount
		} else {
			vatMap[dailyVatInfo.TaxRate] = &VatInfo{
				Amount:    dailyVatInfo.Amount,
				TaxRate:   dailyVatInfo.TaxRate,
				TaxAmount: dailyVatInfo.TaxAmount,
			}
		}
	}

	// Convert map back to slice
	mergedVatInfos := make([]VatInfo, 0, len(vatMap))
	for _, vatInfo := range vatMap {
		mergedVatInfos = append(mergedVatInfos, *vatInfo)
	}

	// Marshal back to JSON
	mergedJSON, err := entities.ConvertDataToJSON(&mergedVatInfos)
	if err != nil {
		d.logger.Errorf("Failed to marshal merged VAT info: %v", err)
		return monthlyVat
	}

	return mergedJSON
}
