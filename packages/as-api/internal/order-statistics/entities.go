package orderstatistics

import (
	"time"
)

type OrderStatisticsDaily struct {
	Date                 time.Time
	CountryCode          *string
	RegionID             *string
	OrderCount           int32
	OrderAmount          float64
	SalesAmount          float64
	ProductPrice         float64
	VatSeller            []*VatInfo
	VatMp                []*VatInfo
	MemberDiscountAmount float64
	SalesFeesAmount      float64
	PurchaseFeesAmount   float64
}

type VatInfo struct {
	Amount    float64 `json:"amount,omitempty"`
	TaxRate   float64 `json:"tax_rate,omitempty"`
	TaxAmount float64 `json:"tax_amount,omitempty"`
}

type OrderStatisticsItem struct {
	BuyerCountryCode  string
	BuyerRegionID     *string
	SellerCountryCode string
	SellerRegionID    *string

	OrderAmount          float64
	SalesAmount          float64
	ProductPrice         float64
	MemberDiscountAmount float64
	SalesFeesAmount      float64
	PurchaseFeesAmount   float64

	TaxInfos []*TaxItemInfo
}

type User struct {
	CountryCode string
	RegionID    *string
}

type Address struct {
	IsDifferentFromResidence bool    `json:"isDifferentFromResidence,omitempty"`
	RegionID                 *string `json:"regionId,omitempty"`
}

type SalesFeeInfo struct {
	Amount         *float64       `json:"amount,omitempty"`
	Rate           *float64       `json:"rate,omitempty"`
	TaxRate        *float64       `json:"tax_rate,omitempty"`
	TaxAmount      *float64       `json:"tax_amount,omitempty"`
	TaxBreakdown   []*TaxItemInfo `json:"tax_breakdown,omitempty"`
	IsTaxProcessed bool           `json:"is_tax_processed,omitempty"`
	SalesTaxAmount *float64       `json:"sales_tax_amount,omitempty"`
}

type MemberDiscountInfo struct {
	Rate   *float64 `json:"rate,omitempty"`
	Amount *float64 `json:"amount,omitempty"`
}

type PurchaseFeeInfo struct {
	Rate         *float64       `json:"rate,omitempty"`
	Amount       *float64       `json:"amount,omitempty"`
	TaxRate      *float64       `json:"tax_rate,omitempty"`
	TaxAmount    *float64       `json:"tax_amount,omitempty"`
	TaxBreakdown []*TaxItemInfo `json:"tax_breakdown,omitempty"`
}

type Country struct {
	Code        string             `json:"code,omitempty"`
	DisplayName *map[string]string `json:"display_name,omitempty"`
	TaxName     *string            `json:"tax_name,omitempty"`
	TaxConfig   *TaxConfig         `json:"tax_config,omitempty"`
}

type Region struct {
	ID          *string            `json:"id,omitempty"`
	Code        *string            `json:"code,omitempty"`
	DisplayName *map[string]string `json:"display_name,omitempty"`
	TaxConfig   *TaxConfig         `json:"tax_config,omitempty"`
}

type DistrictTax struct {
	SpecialRate   *float64 `json:"special_rate,omitempty"`
	TaxRegionName string   `json:"tax_region_name,omitempty"`
	ZipCode       string   `json:"zip_code,omitempty"`
}

type TaxDetail struct {
	TaxRate   float64 `json:"tax_rate"`
	Amount    float64 `json:"amount"`
	TaxAmount float64 `json:"tax_amount"`
}

type TaxItemInfo struct {
	Country  Country      `json:"country"`
	Region   *Region      `json:"region"`
	District *DistrictTax `json:"district_tax"`
	Taxes    []*TaxDetail `json:"taxes"`
}

type TaxConfig struct {
	IsMarketplaceOperators bool `json:"is_marketplace_operators,omitempty"`
}

type ShippingAddress struct {
	Country  *string
	RegionId *string
}
