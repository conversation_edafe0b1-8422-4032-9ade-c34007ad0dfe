package repository

import (
	"context"
	"log"

	"as-api/as/foundations/db/dao"
	"as-api/as/foundations/db/entities"
	"as-api/as/foundations/db/factory"
	"as-api/as/internal/order-statistics/model"
	"as-api/as/pkg/di"
	"as-api/as/pkg/time"

	"github.com/pkg/errors"
	"gorm.io/gen/field"
)

func init() {
	if err := di.RegisterProviders(NewOrderStatisticsRepository); err != nil {
		log.Fatal("register constructor order statistics repository failed:", err)
	}
}

type OrderStatisticsRepository interface {
	GetRegionsByIDs(ctx context.Context, ids []string) ([]*model.Region, error)
	GetOrdersCompletedByDate(ctx context.Context, startDate, endDate time.Time) ([]*model.OrderDetail, error)
	GetOrderStatisticsDailyByYearAndMonth(ctx context.Context, year, month int) ([]*entities.OrderStatisticsDaily, error)
	GetOrderStatisticsMonthly(ctx context.Context, year, month int, countryCode, regionID *string) ([]*entities.OrderStatisticsMonthly, error)
	UpsertOrderStatisticsDaily(ctx context.Context, date time.Time, entities []*entities.OrderStatisticsDaily) error
	UpsertOrderStatisticsMonthly(ctx context.Context, year, month int, entities []*entities.OrderStatisticsMonthly) error
}

type repository struct {
	db *factory.DO
}

func NewOrderStatisticsRepository(db *factory.DO) OrderStatisticsRepository {
	return &repository{
		db: db,
	}
}

func (r *repository) GetOrdersCompletedByDate(ctx context.Context, startDate, endDate time.Time) ([]*model.OrderDetail, error) {
	db := r.db.Model(&model.OrderDetail{})
	odq := db.Query.OrderDetail

	q := db.WithContext(ctx)

	q = q.Where(
		odq.TransactionStatus.Eq("completed"),
		odq.CompletedDate.Gte(startDate), odq.CompletedDate.Lt(endDate),
	)

	preloads := []field.RelationField{
		field.NewRelation("Order", ""),
		field.NewRelation("Product.Seller.User", ""),
	}

	for _, preload := range preloads {
		q = q.Preload(preload)
	}

	orders, err := q.Find()
	if err != nil {
		return nil, errors.Wrap(err, "get orders completed today")
	}

	return orders.([]*model.OrderDetail), nil
}

func (r *repository) GetOrderStatisticsDailyByYearAndMonth(ctx context.Context, year, month int) ([]*entities.OrderStatisticsDaily, error) {
	db := r.db.Model(nil)
	osq := db.Query.OrderStatisticsDaily

	q := osq.WithContext(ctx)
	q = q.Where(osq.Year.Eq(int32(year)), osq.Month.Eq(int32(month)))

	orders, err := q.Find()
	if err != nil {
		return nil, errors.Wrap(err, "get order statistics daily by date")
	}

	return orders, nil
}

func (r *repository) UpsertOrderStatisticsDaily(ctx context.Context, date time.Time, entities []*entities.OrderStatisticsDaily) error {
	db := r.db.Model(nil)
	osq := db.Query.OrderStatisticsDaily

	if err := db.Query.Transaction(func(tx *dao.Query) error {
		q := osq.WithContext(ctx)

		q = q.Where(osq.Year.Eq(int32(date.Year())), osq.Month.Eq(int32(date.Month())), osq.Day.Eq(int32(date.Day())))
		if _, err := q.Delete(); err != nil {
			return errors.Wrap(err, "delete order statistics daily")
		}

		if err := q.Create(entities...); err != nil {
			return errors.Wrap(err, "upsert order statistics daily")
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "upsert order statistics daily")
	}

	return nil
}

func (r *repository) GetRegionsByIDs(ctx context.Context, ids []string) ([]*model.Region, error) {
	db := r.db.Model(&model.Region{})
	regionQ := db.Query.Region
	query := db.WithContext(ctx)

	query = query.Where(regionQ.ID.In(ids...))

	regions, err := query.Preload(field.Associations).Find()
	if err != nil {
		return nil, errors.Wrap(err, "get regions by ids")
	}

	return regions.([]*model.Region), nil
}

func (r *repository) GetOrderStatisticsMonthly(ctx context.Context, year, month int, countryCode, regionID *string) ([]*entities.OrderStatisticsMonthly, error) {
	db := r.db.Model(nil)
	osq := db.Query.OrderStatisticsMonthly
	regionQ := db.Query.Region

	q := osq.WithContext(ctx)
	q = q.LeftJoin(regionQ, regionQ.ID.EqCol(osq.RegionID))

	q = q.Where(osq.Year.Eq(int32(year)), osq.Month.Eq(int32(month)))

	// Apply filters if provided
	if countryCode != nil {
		q = q.Where(osq.CountryCode.Eq(*countryCode))
	}
	if regionID != nil {
		q = q.Where(regionQ.Where(regionQ.ParentID.Eq(*regionID)).Or(regionQ.ID.Eq(*regionID)))
	} else {
		q = q.Where(regionQ.ParentID.IsNull())
	}

	stats, err := q.Find()
	if err != nil {
		return nil, errors.Wrap(err, "get order statistics monthly")
	}

	return stats, nil
}

func (r *repository) UpsertOrderStatisticsMonthly(ctx context.Context, year, month int, entities []*entities.OrderStatisticsMonthly) error {
	db := r.db.Model(nil)
	osq := db.Query.OrderStatisticsMonthly

	if err := db.Query.Transaction(func(tx *dao.Query) error {
		q := osq.WithContext(ctx)

		q = q.Where(osq.Year.Eq(int32(year)), osq.Month.Eq(int32(month)))
		if _, err := q.Delete(); err != nil {
			return errors.Wrap(err, "delete order statistics monthly")
		}

		if err := q.Create(entities...); err != nil {
			return errors.Wrap(err, "upsert order statistics monthly")
		}
		return nil
	}); err != nil {
		return errors.Wrap(err, "upsert order statistics monthly")
	}

	return nil
}
