package orderstatistics

import (
	"as-api/as/foundations/db/entities"
	"as-api/as/internal/order-statistics/model"
	"as-api/as/pkg/helpers/pointer"

	"github.com/pkg/errors"
)

func convertSliceOrderStatisticsDailyToEntities(ents []*OrderStatisticsDaily) ([]*entities.OrderStatisticsDaily, error) {
	result := make([]*entities.OrderStatisticsDaily, len(ents))
	for i, entity := range ents {
		entity, err := convertOrderStatisticsDailyToEntities(entity)
		if err != nil {
			return nil, errors.Wrap(err, "convert order statistics daily to entities")
		}
		result[i] = entity
	}
	return result, nil
}

func convertOrderStatisticsDailyToEntities(entity *OrderStatisticsDaily) (*entities.OrderStatisticsDaily, error) {
	if entity == nil {
		return nil, nil
	}

	vatSeller, err := entities.ConvertDataToJSON(&entity.VatSeller)
	if err != nil {
		return nil, errors.Wrap(err, "convert vat seller to entity")
	}

	vatMp, err := entities.ConvertDataToJSON(&entity.VatMp)
	if err != nil {
		return nil, errors.Wrap(err, "convert vat mp to entity")
	}

	return &entities.OrderStatisticsDaily{
		Year:                 int32(entity.Date.Year()),
		Month:                int32(entity.Date.Month()),
		Day:                  int32(entity.Date.Day()),
		CountryCode:          entity.CountryCode,
		RegionID:             entity.RegionID,
		OrderCount:           pointer.Ptr(entity.OrderCount),
		OrderAmount:          pointer.Ptr(entity.OrderAmount),
		SalesAmount:          pointer.Ptr(entity.SalesAmount),
		ProductPrice:         pointer.Ptr(entity.ProductPrice),
		VatSeller:            vatSeller,
		VatMp:                vatMp,
		MemberDiscountAmount: pointer.Ptr(entity.MemberDiscountAmount),
		SalesFeesAmount:      pointer.Ptr(entity.SalesFeesAmount),
		PurchaseFeesAmount:   pointer.Ptr(entity.PurchaseFeesAmount),
	}, nil
}

func convertOrderStatisticsItemFromModel(entity *model.OrderDetail) (*OrderStatisticsItem, error) {
	if entity == nil {
		return nil, errors.New("entity is nil")
	}

	if entity.Order == nil {
		return nil, errors.New("order is nil")
	}

	if entity.Product == nil || entity.Product.Seller == nil || entity.Product.Seller.User == nil {
		return nil, errors.New("seller is nil")
	}

	buyer, err := convertBuyerFromModel(entity.Order)
	if err != nil {
		return nil, errors.Wrap(err, "convert buyer to entity")
	}

	seller, err := convertSellerFromModel(entity.Product.Seller.User)
	if err != nil {
		return nil, errors.Wrap(err, "convert seller to entity")
	}

	purchaseFeeInfo, err := entities.ConvertJSONToData[PurchaseFeeInfo](entity.PurchaseFeeInfo)
	if err != nil {
		return nil, errors.Wrap(err, "convert purchase fee info to entity")
	}
	if purchaseFeeInfo == nil {
		purchaseFeeInfo = &PurchaseFeeInfo{}
	}

	salesFeeInfo, err := entities.ConvertJSONToData[SalesFeeInfo](entity.SalesFeeInfo)
	if err != nil {
		return nil, errors.Wrap(err, "convert sales fee info to entity")
	}
	if salesFeeInfo == nil {
		salesFeeInfo = &SalesFeeInfo{}
	}

	memberDiscountInfo, err := entities.ConvertJSONToData[MemberDiscountInfo](entity.MemberDiscountInfo)
	if err != nil {
		return nil, errors.Wrap(err, "convert member discount info to entity")
	}
	if memberDiscountInfo == nil {
		memberDiscountInfo = &MemberDiscountInfo{}
	}

	taxInfos, err := entities.ConvertJSONToData[[]*TaxItemInfo](entity.TaxInfos)
	if err != nil {
		return nil, errors.Wrap(err, "convert tax infos to entity")
	}
	if taxInfos == nil {
		taxInfos = &[]*TaxItemInfo{}
	}

	return &OrderStatisticsItem{
		BuyerCountryCode:     buyer.CountryCode,
		BuyerRegionID:        buyer.RegionID,
		SellerCountryCode:    seller.CountryCode,
		SellerRegionID:       seller.RegionID,
		OrderAmount:          pointer.Safe(entity.TotalPrice),
		SalesAmount:          pointer.Safe(entity.SalesAmount),
		ProductPrice:         entity.Price,
		MemberDiscountAmount: pointer.Safe(memberDiscountInfo.Amount),
		SalesFeesAmount:      pointer.Safe(salesFeeInfo.Amount) + pointer.Safe(salesFeeInfo.TaxAmount),
		PurchaseFeesAmount:   pointer.Safe(purchaseFeeInfo.Amount) + pointer.Safe(purchaseFeeInfo.TaxAmount),
		TaxInfos:             pointer.Safe(taxInfos),
	}, nil
}

func convertBuyerFromModel(entity *model.Order) (*User, error) {
	if entity == nil {
		return nil, errors.New("user is nil")
	}

	address, err := entities.ConvertJSONToData[ShippingAddress](entity.ShippingAddress)
	if err != nil {
		return nil, errors.Wrap(err, "convert shipping address to entity")
	}

	if address == nil {
		return nil, errors.New("address is nil")
	}

	return &User{
		CountryCode: pointer.Safe(address.Country),
		RegionID:    address.RegionId,
	}, nil
}

func convertSellerFromModel(entity *model.User) (*User, error) {
	if entity == nil {
		return nil, errors.New("user is nil")
	}

	if entity.CountryCode == nil {
		return nil, errors.New("country code is nil")
	}

	address, err := entities.ConvertJSONToData[Address](entity.HomeAddress)
	if err != nil {
		return nil, errors.Wrap(err, "convert home address to entity")
	}

	if address == nil {
		return nil, errors.New("address is nil")
	}

	return &User{
		CountryCode: pointer.Safe(entity.CountryCode),
		RegionID:    address.RegionID,
	}, nil
}
