package orderstatistics

import (
	"testing"

	"as-api/as/foundations/db/entities"
	"as-api/as/pkg/helpers/pointer"

	"github.com/stretchr/testify/assert"
)

// TestAggregateDailyToMonthly tests the core aggregation logic

func TestAggregateDailyToMonthly(t *testing.T) {
	year := 2024
	month := 11

	domain := &orderStatisticsDomain{}

	tests := []struct {
		name           string
		dailyStats     []*entities.OrderStatisticsDaily
		expectedCount  int
		validateResult func(t *testing.T, result []*entities.OrderStatisticsMonthly)
	}{
		{
			name: "Aggregates multiple days for same country",
			dailyStats: []*entities.OrderStatisticsDaily{
				{
					Year:        int32(year),
					Month:       int32(month),
					Day:         1,
					CountryCode: pointer.Ptr("US"),
					OrderCount:  pointer.Ptr(int32(10)),
					OrderAmount: pointer.Ptr(100.0),
				},
				{
					Year:        int32(year),
					Month:       int32(month),
					Day:         2,
					CountryCode: pointer.Ptr("US"),
					OrderCount:  pointer.Ptr(int32(5)),
					OrderAmount: pointer.Ptr(50.0),
				},
			},
			expectedCount: 1,
			validateResult: func(t *testing.T, result []*entities.OrderStatisticsMonthly) {
				assert.Equal(t, 1, len(result))
				assert.Equal(t, "US", *result[0].CountryCode)
				assert.Equal(t, int32(15), *result[0].OrderCount)
				assert.Equal(t, 150.0, *result[0].OrderAmount)
			},
		},
		{
			name: "Separates different countries",
			dailyStats: []*entities.OrderStatisticsDaily{
				{
					Year:        int32(year),
					Month:       int32(month),
					Day:         1,
					CountryCode: pointer.Ptr("US"),
					OrderCount:  pointer.Ptr(int32(10)),
				},
				{
					Year:        int32(year),
					Month:       int32(month),
					Day:         1,
					CountryCode: pointer.Ptr("JP"),
					OrderCount:  pointer.Ptr(int32(20)),
				},
			},
			expectedCount: 2,
			validateResult: func(t *testing.T, result []*entities.OrderStatisticsMonthly) {
				assert.Equal(t, 2, len(result))
				// Check both countries are present
				countries := make(map[string]int32)
				for _, stat := range result {
					countries[*stat.CountryCode] = *stat.OrderCount
				}
				assert.Equal(t, int32(10), countries["US"])
				assert.Equal(t, int32(20), countries["JP"])
			},
		},
		{
			name: "Handles regions within countries",
			dailyStats: []*entities.OrderStatisticsDaily{
				{
					Year:        int32(year),
					Month:       int32(month),
					Day:         1,
					CountryCode: pointer.Ptr("US"),
					RegionID:    pointer.Ptr("region1"),
					OrderCount:  pointer.Ptr(int32(10)),
				},
				{
					Year:        int32(year),
					Month:       int32(month),
					Day:         1,
					CountryCode: pointer.Ptr("US"),
					RegionID:    pointer.Ptr("region2"),
					OrderCount:  pointer.Ptr(int32(20)),
				},
			},
			expectedCount: 2,
			validateResult: func(t *testing.T, result []*entities.OrderStatisticsMonthly) {
				assert.Equal(t, 2, len(result))
				// Both should have US as country but different regions
				for _, stat := range result {
					assert.Equal(t, "US", *stat.CountryCode)
					assert.NotNil(t, stat.RegionID)
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := domain.aggregateDailyToMonthly(tt.dailyStats, year, month)

			assert.Equal(t, tt.expectedCount, len(result))
			if tt.validateResult != nil {
				tt.validateResult(t, result)
			}
		})
	}
}

func TestCreateStatsKey(t *testing.T) {
	domain := &orderStatisticsDomain{}

	tests := []struct {
		name        string
		countryCode *string
		regionID    *string
		expected    string
	}{
		{
			name:        "Country and region",
			countryCode: pointer.Ptr("US"),
			regionID:    pointer.Ptr("region1"),
			expected:    "US_region1",
		},
		{
			name:        "Country only",
			countryCode: pointer.Ptr("US"),
			regionID:    nil,
			expected:    "US",
		},
		{
			name:        "No country or region",
			countryCode: nil,
			regionID:    nil,
			expected:    "global",
		},
		{
			name:        "Empty country",
			countryCode: pointer.Ptr(""),
			regionID:    pointer.Ptr("region1"),
			expected:    "global",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := domain.createStatsKey(tt.countryCode, tt.regionID)
			assert.Equal(t, tt.expected, result)
		})
	}
}
