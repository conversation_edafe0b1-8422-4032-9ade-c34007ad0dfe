-- Order Statistics Daily Table
-- This migration creates tables to store daily order statistics aggregated by seller, country, and region
-- Includes all the specific metrics required for order statistics data aggregation

-- Daily Order Statistics Table
-- Stores daily aggregated order statistics for each seller by country and region
CREATE TABLE IF NOT EXISTS order_statistics_daily (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Time dimension
    year INTEGER NOT NULL,
    month INTEGER NOT NULL,
    day INTEGER NOT NULL,
    
    -- Geographic dimensions
    country_code VARCHAR(255),
    region_id UUID,
    
    -- Order Count Statistics
    order_count INTEGER DEFAULT 0,
    
    -- Order Value Statistics
    order_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Confirmed Revenue (Sales amount including tax that seller receives)
    sales_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Product Sales Value (Sum of product prices sold by seller, excluding fees/taxes)
    product_price DECIMAL(15,2) DEFAULT 0.00,
    
    -- Seller VAT Statistics (aggregated by tax rate)
    vat_seller JSONB,
    
    -- MP VAT Statistics (Revenue from orders where MP must pay VAT on behalf of seller)
    vat_mp JSONB,
    
    -- Member Discount Statistics
    member_discount_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Sales Fees Statistics (Marketplace sales fees including tax)
    sales_fees_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Purchase Fees Statistics (Marketplace purchase fees including tax)
    purchase_fees_amount DECIMAL(15,2) DEFAULT 0.00
);

-- Monthly Order Statistics Table
-- Stores monthly aggregated order statistics for each seller by country and region
CREATE TABLE IF NOT EXISTS order_statistics_monthly (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Time dimension
    year INTEGER NOT NULL,
    month INTEGER NOT NULL,
    
    -- Geographic dimensions
    country_code VARCHAR(255),
    region_id UUID,
    
    -- Order Count Statistics
    order_count INTEGER DEFAULT 0,
    
    -- Order Value Statistics
    order_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Confirmed Revenue (Sales amount including tax that seller receives)
    sales_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Product Sales Value (Sum of product prices sold by seller, excluding fees/taxes)
    product_price DECIMAL(15,2) DEFAULT 0.00,
    
    -- Seller VAT Statistics (aggregated by tax rate)
    vat_seller JSONB,
    
    -- MP VAT Statistics (Revenue from orders where MP must pay VAT on behalf of seller)
    vat_mp JSONB,
    
    -- Member Discount Statistics
    member_discount_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Sales Fees Statistics (Marketplace selling fees including tax)
    sales_fees_amount DECIMAL(15,2) DEFAULT 0.00,
    
    -- Purchase Fees Statistics (Marketplace buying fees including tax)
    purchase_fees_amount DECIMAL(15,2) DEFAULT 0.00
);
