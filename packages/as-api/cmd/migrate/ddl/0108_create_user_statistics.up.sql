-- Create user_statistics table for storing calculated user statistics
CREATE UNLOGGED TABLE IF NOT EXISTS user_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Grouping dimensions
    country VARCHAR(255),
    region_id UUID,
    
    -- User type classification
    user_type VARCHAR(50) NOT NULL, -- 'seller', 'premium', 'general'
    
    -- Statistics counts
    total_count INTEGER NOT NULL DEFAULT 0,
    active_count INTEGER NOT NULL DEFAULT 0
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_user_statistics_country ON user_statistics(country);
CREATE INDEX IF NOT EXISTS idx_user_statistics_region_id ON user_statistics(region_id);
CREATE INDEX IF NOT EXISTS idx_user_statistics_user_type ON user_statistics(user_type);

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_user_statistics_country_type ON user_statistics(country, user_type);
CREATE INDEX IF NOT EXISTS idx_user_statistics_region_type ON user_statistics(region_id, user_type);
CREATE INDEX IF NOT EXISTS idx_user_statistics_country_region_type ON user_statistics(country, region_id, user_type);
