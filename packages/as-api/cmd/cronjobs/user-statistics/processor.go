package userstatistics

import (
	"context"
	"log"

	"as-api/as/foundations/logger"
	"as-api/as/internal/cms"
	userstatistics "as-api/as/internal/user-statistics"
	"as-api/as/pkg/di"

	"github.com/pkg/errors"
)

func init() {
	if err := di.RegisterProviders(NewUserStatisticsProcessor); err != nil {
		log.Fatal("register constructor user statistics processor failed:", err)
	}
}

// UserStatisticsProcessor defines the interface for user statistics cronjob operations
type UserStatisticsProcessor interface {
	// Daily operations
	CalculateUserStatisticsDaily(ctx context.Context) error
}

type userStatisticsProcessor struct {
	cmsDomain            cms.CmsDomain
	userStatisticsDomain userstatistics.UserStatisticsDomain
	logger               logger.Logger
}

// NewUserStatisticsProcessor creates a new user statistics processor instance
func NewUserStatisticsProcessor(
	userStatisticsDomain userstatistics.UserStatisticsDomain,
	cmsDomain cms.CmsDomain,
	logger logger.Logger,
) UserStatisticsProcessor {
	return &userStatisticsProcessor{
		userStatisticsDomain: userStatisticsDomain,
		cmsDomain:            cmsDomain,
		logger:               logger,
	}
}

// CalculateUserStatisticsDaily calculates user statistics for yesterday
func (p *userStatisticsProcessor) CalculateUserStatisticsDaily(ctx context.Context) error {
	p.logger.Infof("Starting daily user statistics calculation")

	countries, err := p.userStatisticsDomain.GetAllCountryCodes(ctx)
	if err != nil {
		return errors.Wrapf(err, "get countries from cms")
	}

	for _, country := range countries {
		if err := p.CalculateUserStatisticsCountry(ctx, country); err != nil {
			p.logger.Errorf("Failed to calculate user statistics for country %s: %s", country, err)
		}
	}

	p.logger.Infof("Completed daily user statistics calculation")

	return nil
}

func (p *userStatisticsProcessor) CalculateUserStatisticsCountry(ctx context.Context, country string) error {
	p.logger.Infof("Calculating user statistics for country: %s", country)

	if err := p.userStatisticsDomain.CalculateUserStatistics(ctx, country); err != nil {
		p.logger.Errorf("Failed to calculate user statistics for country %s: %s", country, err)
	}

	p.logger.Infof("Completed user statistics calculation for country: %s", country)

	return nil
}
