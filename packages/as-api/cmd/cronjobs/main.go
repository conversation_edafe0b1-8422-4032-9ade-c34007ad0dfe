package main

import (
	"context"
	"log"
	"os"

	emailnotification "as-api/as/cmd/cronjobs/email-notification"
	futurenotification "as-api/as/cmd/cronjobs/future-notification"
	"as-api/as/cmd/cronjobs/homefeed"
	"as-api/as/cmd/cronjobs/inquiry"
	"as-api/as/cmd/cronjobs/master"
	ordercomplete "as-api/as/cmd/cronjobs/order-complete"
	orderstatistics "as-api/as/cmd/cronjobs/order-statistics"
	paymenttimeout "as-api/as/cmd/cronjobs/payment-timeout"
	productavailability "as-api/as/cmd/cronjobs/product-availability"
	salesinsights "as-api/as/cmd/cronjobs/sales-insights"
	salessummary "as-api/as/cmd/cronjobs/sales-summary"
	subscriptionaudit "as-api/as/cmd/cronjobs/subscription-audit"
	subscriptionexpiry "as-api/as/cmd/cronjobs/subscription-expiry"
	userstatistics "as-api/as/cmd/cronjobs/user-statistics"
	"as-api/as/event"
	"as-api/as/foundations/db/factory"
	"as-api/as/foundations/env"
	"as-api/as/foundations/env/aws"
	"as-api/as/foundations/env/files"
	"as-api/as/foundations/logger"
	"as-api/as/foundations/paypalapi"
	"as-api/as/pkg/cron"
	"as-api/as/pkg/di"
	"as-api/as/pkg/helpers/email"
	"as-api/as/pkg/helpers/subscription"
	"as-api/as/pkg/validator"

	"github.com/pkg/errors"
	"go.uber.org/dig"
)

type CronJob struct {
	dig.In

	Master              master.MasterProcessor
	Inquiry             inquiry.InquiryProcessor
	HomeFeed            homefeed.HomeFeedProcessor
	SellerDashboard     homefeed.SellerDashboardProcessor
	FutureNotification  futurenotification.FutureNotificationProcessor
	SalesInsights       salesinsights.SalesInsightsProcessor
	SalesSummary        salessummary.SalesSummaryProcessor
	EmailNotification   emailnotification.EmailNotificationProcessor
	SubscriptionAudit   subscriptionaudit.SubscriptionAuditProcessor
	PaymentTimeout      paymenttimeout.PaymentTimeoutProcessor
	OrderComplete       ordercomplete.OrderCompleteProcessor
	OrderStatistics     orderstatistics.OrderStatisticsProcessor
	SubscriptionExpiry  subscriptionexpiry.SubscriptionExpiryProcessor
	ProductAvailability productavailability.ProductAvailabilityProcessor
	UserStatistics      userstatistics.UserStatisticsProcessor
}

type App struct {
	dig.In

	Env          env.MapperData
	Logger       logger.Logger
	Cron         CronJob
	EventService event.EventService
}

func (a App) setupEvents() {
	// Setup all event handlers using the event service
	a.EventService.SetupHandlers()
}

func (a App) Run() error {
	// Setup event handlers
	a.setupEvents()

	cj, err := cron.NewCronJob()
	if err != nil {
		return errors.Wrap(err, "failed initialize cron job")
	}

	ctx := context.Background()
	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishProductCategories(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish product categories")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishBrands(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish brands")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishLanguages(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish languages")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishUiWords(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish ui words")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishSubscriptionFees(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish subscription fees")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishCommissionSales(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish commission sales")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishTransferFees(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish transfer fees")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishCustomNgWords(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish custom ng words")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishPurchaseFees(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish purchase fees")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_MASTER_BRANCHS_PUBLISH_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Master.PublishCountries(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job publish countries")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_INQUIRY_COUNT_AREA_SERVICE_INQUIRIES_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.Inquiry.CountAreaServiceInquiries(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job count area service inquiries")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_HOMEFEED_CACHE_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.HomeFeed.CacheHomeFeeds(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job cache home feeds")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_SELLER_DASHBOARD_CACHE_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.SellerDashboard.CacheShopYesterdayResults(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job cache seller dashboard")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_FUTURE_NOTIFICATION_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.FutureNotification.ProcessFutureNotifications(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job process future notifications")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_SALES_SUMMARY_DAILY_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.SalesSummary.CalculateDailySummaryForAllSellers(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job calculate daily sales summary")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_SALES_INSIGHTS_DAILY_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.SalesInsights.CalculateFollowerStatisticsDaily(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job calculate follower statistics")
	// }

	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_EMAIL_NOTIFICATION_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.EmailNotification.ProcessDelayedEmailNotifications(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job process delayed email notifications")
	// }

	// // Apple subscription audit - every 3 hours
	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_APPLE_SUBSCRIPTION_AUDIT_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.SubscriptionAudit.AuditAppleSubscriptions(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job apple subscription audit")
	// }

	// // Google subscription audit - every 3 hours with 1 hour offset
	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_GOOGLE_SUBSCRIPTION_AUDIT_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.SubscriptionAudit.AuditGoogleSubscriptions(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job google subscription audit")
	// }

	// // Subscription expiry finalizer - daily at 02:00 UTC
	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_SUBSCRIPTION_EXPIRY_FINALIZER_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.SubscriptionAudit.FinalizeExpiredSubscriptions(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job subscription expiry finalizer")
	// }

	// // Payment timeout processor - every 5 minutes
	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_PAYMENT_TIMEOUT_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.PaymentTimeout.ProcessPaymentTimeouts(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job payment timeout")
	// }

	// // Order complete processor
	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_ORDER_COMPLETE_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.OrderComplete.ProcessOrderComplete(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job order complete")
	// }

	// // Order complete processor
	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_SUBSCRIPTION_EXPIRY_MEMBERSHIP, func(ctx context.Context) {
	// 	if err := a.Cron.SubscriptionExpiry.ProcessExpiredSubscriptions(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job subscription expiry")
	// }

	// // Product availability processor - daily
	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_PRODUCT_AVAILABILITY_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.ProductAvailability.ProcessProductAvailability(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job product availability")
	// }

	// // User statistics processor - daily at 03:00 UTC (off-peak hours)
	// if err := cj.AddFunc(ctx, a.Env.CRONJOB_USER_STATISTICS_TIME, func(ctx context.Context) {
	// 	if err := a.Cron.UserStatistics.CalculateUserStatisticsDaily(ctx); err != nil {
	// 		a.Logger.Error(err.Error())
	// 	}
	// }); err != nil {
	// 	return errors.Wrap(err, "failed add cron job user statistics")
	// }

	if err := cj.AddFunc(ctx, a.Env.CRONJOB_ORDER_STATISTICS_DAILY_TIME, func(ctx context.Context) {
		if err := a.Cron.OrderStatistics.CalculateDailyStatisticsForAllSellers(ctx); err != nil {
			a.Logger.Error(err.Error())
		}
	}); err != nil {
		return errors.Wrap(err, "failed add cron job order statistics daily")
	}

	if err := cj.AddFunc(ctx, a.Env.CRONJOB_ORDER_STATISTICS_MONTHLY_TIME, func(ctx context.Context) {
		if err := a.Cron.OrderStatistics.CalculateMonthlyStatisticsForAllSellers(ctx); err != nil {
			a.Logger.Error(err.Error())
		}
	}); err != nil {
		return errors.Wrap(err, "failed add cron job order statistics monthly")
	}

	cj.StartCronJob()

	return nil
}

func setup() error {
	// logger
	if err := di.RegisterProviders(logger.NewLogger); err != nil {
		return errors.Wrap(err, "register constructor logger failed")
	}

	// validator
	if err := di.RegisterProviders(validator.NewValidator); err != nil {
		return errors.Wrap(err, "register constructor validator failed")
	}

	// env
	if os.Getenv("ENV") == "develop" {
		if err := di.RegisterProviders(files.New); err != nil {
			return errors.Wrap(err, "register constructor env failed")
		}
	} else {
		if err := di.RegisterProviders(aws.New); err != nil {
			return errors.Wrap(err, "register constructor env failed")
		}
	}
	if err := di.RegisterProviders(env.New); err != nil {
		return errors.Wrap(err, "register constructor env failed")
	}
	if err := di.RegisterProviders(env.ENV.Load); err != nil {
		return errors.Wrap(err, "load env failed")
	}

	// DB
	if err := di.RegisterProviders(configDB); err != nil {
		return errors.Wrap(err, "load config db failed")
	}
	if err := di.RegisterProviders(factory.NewConnection); err != nil {
		return errors.Wrap(err, "load db manager failed")
	}
	if err := di.RegisterProviders(factory.NewDO); err != nil {
		return errors.Wrap(err, "load do failed")
	}

	// Email
	if err := di.RegisterProviders(configEmail); err != nil {
		return errors.Wrap(err, "load config email failed")
	}
	if err := di.RegisterProviders(email.New); err != nil {
		return errors.Wrap(err, "load email service failed")
	}

	// Subscription service
	if err := di.RegisterProviders(configSubscription); err != nil {
		return errors.Wrap(err, "load config subscription failed")
	}
	if err := di.RegisterProviders(subscription.New); err != nil {
		return errors.Wrap(err, "load subscription service failed")
	}

	// PayPal
	if err := di.RegisterProviders(configPayPal); err != nil {
		return errors.Wrap(err, "load config paypal failed")
	}

	if err := di.RegisterProviders(paypalapi.NewPayPalClient); err != nil {
		return errors.Wrap(err, "load paypal client failed")
	}

	return nil
}

func configDB(e env.MapperData) factory.Config {
	return factory.Config{
		User:     e.PostgresUser,
		Password: e.PostgresPassword,
		Host:     e.PostgresHostname,
		Database: e.PostgresDatabase,
		Port:     e.PostgresPort,
	}
}

func configEmail(e env.MapperData) email.Config {
	return email.Config{
		Host:        e.SMTPHost,
		Port:        e.SMTPPort,
		Username:    e.SMTPUsername,
		Password:    e.SMTPPassword,
		From:        e.SMTPFrom,
		ServiceName: e.ServiceName,
	}
}

func configSubscription(e env.MapperData) subscription.Config {
	return subscription.Config{
		PackageName:       e.PackageName,
		AppleVerifyURL:    e.AppleVerifyURL,
		ServiceAccountKey: e.GoogleSubscriptionServiceAccount,
		AppleSharedSecret: e.AppleSharedSecret,
		AppleRootCert:     e.AppleRootCert,
	}
}

func configPayPal(e env.MapperData) paypalapi.PayPalConfig {
	return paypalapi.PayPalConfig{
		BaseURL:      e.PayPalBaseURL,
		ClientID:     e.PayPalClientID,
		ClientSecret: e.PayPalClientSecret,
	}
}

func main() {
	// setup providers
	if err := setup(); err != nil {
		log.Fatal("register common providers:", err)
	}

	// run app
	if err := di.Run(App.Run); err != nil {
		log.Fatal("run failed:", err)
	}
}
