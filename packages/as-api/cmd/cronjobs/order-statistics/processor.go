package orderstatistics

import (
	"context"
	"log"

	"as-api/as/foundations/logger"
	orderstatistics "as-api/as/internal/order-statistics"
	"as-api/as/pkg/di"
	pkgtime "as-api/as/pkg/time"

	"github.com/pkg/errors"
)

func init() {
	if err := di.Register<PERSON>iders(NewOrderStatisticsProcessor); err != nil {
		log.Fatal("register constructor order statistics processor failed:", err)
	}
}

// OrderStatisticsProcessor defines the interface for order statistics cronjob operations
type OrderStatisticsProcessor interface {
	// Daily operations
	CalculateDailyStatisticsForAllSellers(ctx context.Context) error
	CalculateMonthlyStatisticsForAllSellers(ctx context.Context) error
}

type orderStatisticsProcessor struct {
	orderStatisticsDomain orderstatistics.OrderStatisticsDomain
	logger                logger.Logger
}

// NewOrderStatisticsProcessor creates a new order statistics processor instance
func NewOrderStatisticsProcessor(
	orderStatisticsDomain orderstatistics.OrderStatisticsDomain,
	logger logger.Logger,
) OrderStatisticsProcessor {
	return &orderStatisticsProcessor{
		orderStatisticsDomain: orderStatisticsDomain,
		logger:                logger,
	}
}

// CalculateDailyStatisticsForAllSellers calculates daily statistics for all sellers for yesterday
func (p *orderStatisticsProcessor) CalculateDailyStatisticsForAllSellers(ctx context.Context) error {
	// Calculate for yesterday (completed transactions are typically from previous day)
	today := pkgtime.TodayInDefaultTZ()

	p.logger.Infof("Starting daily order statistics calculation for all sellers for date: %s", today.Format("2006-01-02"))

	err := p.orderStatisticsDomain.CalculateAllSellersDailyStatistics(ctx, today)
	if err != nil {
		return errors.Wrapf(err, "calculate daily order statistics for all sellers for date %s", today.Format("2006-01-02"))
	}

	p.logger.Infof("Completed daily order statistics calculation for all sellers for date: %s", today.Format("2006-01-02"))

	return nil
}

// CalculateMonthlyStatisticsForAllSellers calculates monthly statistics for all sellers for a specific month
func (p *orderStatisticsProcessor) CalculateMonthlyStatisticsForAllSellers(ctx context.Context) error {
	// Calculate for previous month (to ensure all daily data is available)
	currentMonth := pkgtime.TodayInDefaultTZ().AddDate(0, 0, -1)
	year := currentMonth.Year()
	month := int(currentMonth.Month())

	p.logger.Infof("Starting monthly order statistics calculation for all sellers for %d-%02d", year, month)

	err := p.orderStatisticsDomain.CalculateAllSellersMonthlyStatistics(ctx, year, month)
	if err != nil {
		return errors.Wrapf(err, "calculate monthly order statistics for all sellers for %d-%02d", year, month)
	}

	p.logger.Infof("Completed monthly order statistics calculation for all sellers for %d-%02d", year, month)

	return nil
}
