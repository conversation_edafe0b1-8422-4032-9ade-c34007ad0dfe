openapi: 3.1.0
info:
    title: AS API Admin
    version: "1.0"
    description: This is API specification for AS project.
servers:
    - url: "http://localhost:3000/admin/v1"
paths:
  /auth:
    post:
      $ref: ./paths/auth/post.yml
  /system/maintenance-status:
    get:
      $ref: ./paths/system/maintenance-status/get.yml
    put:
      $ref: ./paths/system/maintenance-status/put.yml
  /system/postal-codes:
    get:
      $ref: ./paths/system/postal-codes/get.yml
  /cms/content-categories:
    get:
      $ref: ./paths/cms/content-categories/get.yml
  /cms/contents:
    get:
      $ref: ./paths/cms/contents/get.yml
    post:
      $ref: ./paths/cms/contents/post.yml
  /cms/contents/{id}:
    get:
      $ref: ./paths/cms/contents/id/get.yml
    put:
      $ref: ./paths/cms/contents/id/put.yml
    delete:
      $ref: ./paths/cms/contents/id/delete.yml
  /cms/languages:
    get:
      $ref: ./paths/cms/languages/get.yml
  /cms/notifications:
    get:
      $ref: ./paths/cms/notifications/get.yml
    post:
      $ref: ./paths/cms/notifications/post.yml
  /cms/notifications/{id}:
    get:
      $ref: ./paths/cms/notifications/id/get.yml
    put:
      $ref: ./paths/cms/notifications/id/put.yml
    delete:
      $ref: ./paths/cms/notifications/id/delete.yml
  /cms/faq-categories-branches:
    get:
      $ref: ./paths/cms/faq-categories-branches/get.yml
    post:
      $ref: ./paths/cms/faq-categories-branches/post.yml
  /cms/faq-categories-branches/{id}:
    get:
      $ref: ./paths/cms/faq-categories-branches/id/get.yml
  /cms/terms-branches:
    get:
      $ref: ./paths/cms/terms-branches/get.yml
    post:
      $ref: ./paths/cms/terms-branches/post.yml
  /cms/terms-branches/{id}:
    get:
      $ref: ./paths/cms/terms-branches/id/get.yml
    put:
      $ref: ./paths/cms/terms-branches/id/put.yml
    delete:
      $ref: ./paths/cms/terms-branches/id/delete.yml
  /cms/terms-branches/{id}/publish:
    post:
      $ref: ./paths/cms/terms-branches/id/publish/post.yml
    delete:
      $ref: ./paths/cms/terms-branches/id/publish/delete.yml
  /users:
    get:
      $ref: ./paths/users/get.yml
  /users/{id}:
    get:
      $ref: ./paths/users/id/get.yml
    put:
      $ref: ./paths/users/id/put.yml
  /users/{id}/status:
    put:
      $ref: ./paths/users/id/status/put.yml
  /user-statistics/global-summary:
    get:
      $ref: ./paths/user-statistics/global-summary/get.yml
  /user-statistics/country-summary:
    get:
      $ref: ./paths/user-statistics/country-summary/get.yml
  /user-statistics/region-summary:
    get:
      $ref: ./paths/user-statistics/region-summary/get.yml
  /master/product-categories-branches:
    get:
      $ref: ./paths/master/product-categories-branches/get.yml
    post:
      $ref: ./paths/master/product-categories-branches/post.yml
  /master/product-categories-branches/{id}:
    get:
      $ref: ./paths/master/product-categories-branches/id/get.yml
    put:
      $ref: ./paths/master/product-categories-branches/id/put.yml
    delete:
      $ref: ./paths/master/product-categories-branches/id/delete.yml
  /master/product-categories-branches/{id}/publish:
    post:
      $ref: ./paths/master/product-categories-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/product-categories-branches/id/publish/delete.yml
  /master/brands-branches:
    get:
      $ref: ./paths/master/brands-branches/get.yml
    post:
      $ref: ./paths/master/brands-branches/post.yml
  /master/brands-branches/{id}:
    get:
      $ref: ./paths/master/brands-branches/id/get.yml
    put:
      $ref: ./paths/master/brands-branches/id/put.yml
    delete:
      $ref: ./paths/master/brands-branches/id/delete.yml
  /master/brands-branches/{id}/publish:
    post:
      $ref: ./paths/master/brands-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/brands-branches/id/publish/delete.yml
  /master/languages-branches:
    get:
      $ref: ./paths/master/languages-branches/get.yml
    post:
      $ref: ./paths/master/languages-branches/post.yml
  /master/languages-branches/{id}:
    put:
      $ref: ./paths/master/languages-branches/id/put.yml
    delete:
      $ref: ./paths/master/languages-branches/id/delete.yml
  /master/languages-branches/{id}/publish:
    post:
      $ref: ./paths/master/languages-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/languages-branches/id/publish/delete.yml
  /banners:
    get:
      $ref: ./paths/banners/get.yml
    post:
      $ref: ./paths/banners/post.yml
  /banners/{id}:
    get:
      $ref: ./paths/banners/id/get.yml
    put:
      $ref: ./paths/banners/id/put.yml
    delete:
      $ref: ./paths/banners/id/delete.yml
  /products/{id}:
    put:
      $ref: ./paths/products/id/put.yml
    get:
      $ref: ./paths/products/id/get.yml
  /products/{id}/ban:
    put:
      $ref: ./paths/products/id/ban/put.yml
  /products/search:
    post:
      $ref: ./paths/products/search/post.yml
  /sellers/{id}:
    get:
      $ref: ./paths/sellers/id/get.yml
  /sellers/{id}/products:
    get:
      $ref: ./paths/sellers/products/get.yml
  /master/ui-words-branches:
    get:
      $ref: ./paths/master/ui-words-branches/get.yml
    post:
      $ref: ./paths/master/ui-words-branches/post.yml
  /master/ui-words-branches/{id}:
    put:
      $ref: ./paths/master/ui-words-branches/id/put.yml
    delete:
      $ref: ./paths/master/ui-words-branches/id/delete.yml
  /master/ui-words-branches/{id}/publish:
    post:
      $ref: ./paths/master/ui-words-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/ui-words-branches/id/publish/delete.yml
  /uploads:
    put:
      $ref: ./paths/uploads/put.yml
  /emails:
    get:
      $ref: ./paths/users/emails/get.yml
  /master/custom-ng-words-branches:
    get:
      $ref: ./paths/master/custom-ng-words-branches/get.yml
    post:
      $ref: ./paths/master/custom-ng-words-branches/post.yml
  /master/custom-ng-words-branches/{id}:
    put:
      $ref: ./paths/master/custom-ng-words-branches/id/put.yml
    delete:
      $ref: ./paths/master/custom-ng-words-branches/id/delete.yml
  /master/custom-ng-words-branches/{id}/publish:
    post:
      $ref: ./paths/master/custom-ng-words-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/custom-ng-words-branches/id/publish/delete.yml
  /reports:
    get:
      $ref: ./paths/reports/get.yml
  /reports/{id}/status:
    put:
      $ref: ./paths/reports/id/status/put.yml
  /master/subscription-fees-branches:
    get:
      $ref: ./paths/master/subscription-fees-branches/get.yml
    post:
      $ref: ./paths/master/subscription-fees-branches/post.yml
  /master/subscription-fees-branches/{id}:
    put:
      $ref: ./paths/master/subscription-fees-branches/id/put.yml
    delete:
      $ref: ./paths/master/subscription-fees-branches/id/delete.yml
  /master/subscription-fees-branches/{id}/publish:
    post:
      $ref: ./paths/master/subscription-fees-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/subscription-fees-branches/id/publish/delete.yml
  /master/commission-sales-branches:
    get:
      $ref: ./paths/master/commission-sales-branches/get.yml
    post:
      $ref: ./paths/master/commission-sales-branches/post.yml
  /master/commission-sales-branches/{id}:
    put:
      $ref: ./paths/master/commission-sales-branches/id/put.yml
    delete:
      $ref: ./paths/master/commission-sales-branches/id/delete.yml
  /master/commission-sales-branches/{id}/publish:
    post:
      $ref: ./paths/master/commission-sales-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/commission-sales-branches/id/publish/delete.yml
  /master/transfer-fees-branches:
    get:
      $ref: ./paths/master/transfer-fees-branches/get.yml
    post:
      $ref: ./paths/master/transfer-fees-branches/post.yml
  /master/transfer-fees-branches/{id}:
    put:
      $ref: ./paths/master/transfer-fees-branches/id/put.yml
    delete:
      $ref: ./paths/master/transfer-fees-branches/id/delete.yml
  /master/transfer-fees-branches/{id}/publish:
    post:
      $ref: ./paths/master/transfer-fees-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/transfer-fees-branches/id/publish/delete.yml
  /inquiries/stats/area-service:
    get:
      $ref: ./paths/inquiries/stats/area-service/get.yml
  /users/{id}/purchased-items:
    get:
      $ref:  ./paths/users/purchased-items/get.yml
  /sellers/{id}/sold-items:
    get:
      $ref: ./paths/sellers/sold-items/get.yml
  /master/purchase-fees-branches:
    get:
      $ref: ./paths/master/purchase-fees-branches/get.yml
    post:
      $ref: ./paths/master/purchase-fees-branches/post.yml
  /master/purchase-fees-branches/{id}:
    put:
      $ref: ./paths/master/purchase-fees-branches/id/put.yml
    delete:
      $ref: ./paths/master/purchase-fees-branches/id/delete.yml
  /master/purchase-fees-branches/{id}/publish:
    post:
      $ref: ./paths/master/purchase-fees-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/purchase-fees-branches/id/publish/delete.yml
  /master/country-branches:
    get:
      $ref: ./paths/master/country-branches/get.yml
    post:
      $ref: ./paths/master/country-branches/post.yml
  /master/country-branches/{id}:
    put:
      $ref: ./paths/master/country-branches/id/put.yml
    delete:
      $ref: ./paths/master/country-branches/id/delete.yml
  /master/country-branches/{id}/publish:
    post:
      $ref: ./paths/master/country-branches/id/publish/post.yml
    delete:
      $ref: ./paths/master/country-branches/id/publish/delete.yml
  /orders:
    get:
      $ref: ./paths/orders/get.yml
  /orders/{id}:
    get:
      $ref: ./paths/orders/id/get.yml
  /orders/{id}/ban:
    post:
      $ref: ./paths/orders/id/ban/post.yml
  /orders/{id}/cancel:
    post:
      $ref: ./paths/orders/id/cancel/post.yml
  /order-items/{id}:
    get:
      $ref: ./paths/order-items/id/get.yml
  /order-items/{id}/cancel:
    post:
      $ref: ./paths/order-items/id/cancel/post.yml
  /order-items/{id}/hold:
    put:
      $ref: ./paths/order-items/id/hold/put.yml
  /order-items/{id}/unhold:
    put:
      $ref: ./paths/order-items/id/unhold/put.yml
  /order-items/{id}/approve:
    put:
      $ref: ./paths/order-items/id/approve/put.yml
  /order-details:
    get:
      $ref: ./paths/order-details/get.yml
  /users/{id}/notifications:
    post:
      $ref: ./paths/users/id/notifications/post.yml
  /countries:
    get:
      $ref: ./paths/countries/get.yml
  /order-statistics:
    get:
      $ref: ./paths/order-statistics/get.yml
tags:
    - name: auth
    - name: system
    - name: cms
    - name: master
    - name: banner
    - name: product
    - name: seller
    - name: upload
    - name: report
    - name: inquiry
    - name: order
    - name: order-statistics
components:
  schemas:
    # Model
    Pagination:
      $ref: ./components/models/pagination.yml
    MaintenanceStatus:
      $ref: ./components/models/system/maintenance-status.yml
    PostalCodesResponse:
      $ref: ./components/responses/postal-codes/get.response.yml
    Admin:
      $ref: ./components/models/admin.yml
    ContentCategory:
      $ref: ./components/models/cms/content-category.yml
    ContentType:
      $ref: ./components/models/cms/content-type.yml
    ContentField:
      $ref: ./components/models/cms/content-field.yml
    Content:
      $ref: ./components/models/cms/content.yml
    ContentContent:
      $ref: ./components/models/cms/content-content.yml
    TermsType:
      $ref: ./components/models/cms/terms/terms-type.yml
    Terms:
      $ref: ./components/models/cms/terms/terms.yml
    TermsBranch:
      $ref: ./components/models/cms/terms/terms-branch.yml
    User:
      $ref: ./components/models/users/users.yml
    AccountStatus:
      $ref: ./components/models/enum/account-status.yml
    AccountType:
      $ref: ./components/models/enum/account-type.yml
    MasterStatus:
      $ref: ./components/models/master/master-status.yml
    ProductCategoriesBranch:
      $ref: ./components/models/master/product-categories/product-categories-branch.yml
    ProductCategory:
      $ref: ./components/models/master/product-categories/product-category.yml
    SizeTable:
      $ref: ./components/models/master/product-categories/size-table.yml
    BrandsBranch:
      $ref: ./components/models/master/brands/brands-branch.yml
    Brand:
      $ref: ./components/models/master/brands/brand.yml
    LanguagesBranch:
      $ref: ./components/models/master/languages/languages-branch.yml
    Language:
      $ref: ./components/models/master/languages/language.yml
    ProductCondition:
      $ref: ./components/models/product/product-condition.yml
    Product:
      $ref: ./components/models/product/product.yml
    ProductTarget:
      $ref: ./components/models/product/product-target.yml
    CitesCommonField:
      $ref: ./components/models/product/product-cites-information/product-cites-common-field.yml
    ProductSalesStatus:
      $ref: ./components/models/product/sales-status.yml
    ProductTransactionStatus:
      $ref: ./components/models/product/transaction-status.yml
    ProductBrand:
      $ref: ./components/models/product/product-brand.yml
    BannerActionType:
      $ref: ./components/models/banner/banner-action-type.yml
    BannerAction:
      $ref: ./components/models/banner/banner-action.yml
    BannerQuery:
      $ref: ./components/models/banner/banner-query.yml
    BannerTranslation:
      $ref: ./components/models/banner/banner-translation.yml
    BannerStatus:
      $ref: ./components/models/banner/banner-status.yml
    Banner:
      $ref: ./components/models/banner/banner.yml
    OrderBy:
      $ref: ./components/models/enum/order-by.yml
    UiWordsBranch:
      $ref: ./components/models/master/ui-words/ui-words-branch.yml
    UiWordsCsv:
      $ref: ./components/models/master/ui-words/ui-words-csv.yml
    UiWords:
      $ref: ./components/models/master/ui-words/ui-words.yml
    NotificationSettingsType:
      $ref: ./components/models/enum/notification-setting-type.yml
    NotificationType:
      $ref: ./components/models/notification/notification-type.yml
    Email:
      $ref: ./components/models/users/emails/emails.yml
    Notification:
      $ref: ./components/models/notification/notification.yml
    NotificationArticleContent:
      $ref: ./components/models/notification/notification-article-content.yml
    ContentStatus:
      $ref: ./components/models/cms/content-status.yml
    CustomNGWordsBranch:
      $ref: ./components/models/master/custom-ng-words/custom-ng-words-branch.yml
    CustomNGWord:
      $ref: ./components/models/master/custom-ng-words/custom-ng-word.yml
    Seller:
      $ref: ./components/models/seller.yml
    SellerRating:
      $ref: ./components/models/seller/seller-rating.yml
    Report:
      $ref: ./components/models/report/report.yml
    ReportStatus:
      $ref: ./components/models/report/report-status.yml
    ReportItemType:
      $ref: ./components/models/report/report-item-type.yml
    FaqCategory:
      $ref: ./components/models/cms/faq-category.yml
    FaqCategoriesBranch:
      $ref: ./components/models/cms/faq-categories-branch.yml
    Address:
      $ref: ./components/models/address.yml
    Phone:
      $ref: ./components/models/phone.yml
    CreditCard:
      $ref: ./components/models/credit-card.yml
    SubscriptionFeesBranch:
      $ref: ./components/models/master/subscription-fees/subscription-fees-branch.yml
    SubscriptionFee:
      $ref: ./components/models/master/subscription-fees/subscription-fee.yml
    CommissionSalesBranch:
      $ref: ./components/models/master/commission-sales/commission-sales-branch.yml
    CommissionSale:
      $ref: ./components/models/master/commission-sales/commission-sale.yml
    TransferFeesBranch:
      $ref: ./components/models/master/transfer-fees/transfer-fees-branch.yml
    TransferFee:
      $ref: ./components/models/master/transfer-fees/transfer-fee.yml
    InquiryLocationStats:
      $ref: ./components/models/inquiry/inquiry-location-stats.yml
    PurchaseFeesBranch:
      $ref: ./components/models/master/purchase-fees/purchase-fees-branch.yml
    PurchaseFee:
      $ref: ./components/models/master/purchase-fees/purchase-fee.yml
    PurchaseFeeType:
      $ref: ./components/models/master/purchase-fees/purchase-fee-type.yml
    Order:
      $ref: ./components/models/order/order.yml
    PurchasedItem:
      $ref: ./components/models/order/purchased-item.yml
    TaxDetail:
      $ref: ./components/models/order/tax-detail.yml
    Currency:
      $ref: ./components/models/master/country/currency.yml
    Country:
      $ref: ./components/models/master/country/country.yml
    CountryBranch:
      $ref: ./components/models/master/country/country-branch.yml
    BankAccountType:
      $ref: ./components/models/master/country/bank-account-type.yml
    TaxName:
      $ref: ./components/models/master/country/tax-name.yml
    OptionalTax:
      $ref: ./components/models/master/country/optional-tax.yml
    TaxConfig:
      $ref: ./components/models/master/country/tax-config.yml
    RegionTax:
      $ref: ./components/models/master/country/region-tax.yml
    DistrictTax:
      $ref: ./components/models/master/country/district-tax.yml
    SaletaxDatabase:
      $ref: ./components/models/master/country/saletax-database.yml
    TaxInfo:
      $ref: ./components/models/order/tax-info.yml
    TaxItemInfo:
      $ref: ./components/models/order/tax-item-info.yml

    Rating:
      $ref: ./components/models/rating.yml
    CompanyInfo:
      $ref: ./components/models/company-info.yml
    BankAccount:
      $ref: ./components/models/bank-account.yml
    Kyc:
      $ref: ./components/models/kyc.yml
    NotificationContent:
      $ref: ./components/models/notification/notification-content.yml

    # User Statistics Models
    UserStatistics:
      $ref: ./components/models/user-statistics.yml
    CountryStatistics:
      $ref: ./components/models/country-statistics.yml
    RegionStatistics:
      $ref: ./components/models/region-statistics.yml

    # Request
    AuthRequest:
      $ref: ./components/requests/auth/post.request.yml
    MaintenanceUpdateRequest:
      $ref: ./components/requests/system/maintenance-status/put.request.yml
    PublishProductCategoriesBranchRequest:
      $ref: ./components/requests/master/product-categories-branches/id/publish/post.request.yml
    PublishBrandsBranchRequest:
      $ref: ./components/requests/master/brands-branches/id/publish/post.request.yml
    PublishLanguagesBranchRequest:
      $ref: ./components/requests/master/languages-branches/id/publish/post.request.yml
    PublishUiWordsBranchRequest:
      $ref: ./components/requests/master/ui-words-branches/id/publish/post.request.yml
    PublishTermsBranchRequest:
      $ref: ./components/requests/cms/terms-branches/id/publish/post.request.yml
    PublishCountryBranchRequest:
      $ref: ./components/requests/master/country-branches/id/publish/post.request.yml
    SearchProductsRequest:
      $ref: ./components/requests/products/search/post.request.yml
    ProductUpdateRequest:
      $ref: ./components/requests/products/id/put.request.yml
    ProductBanRequest:
      $ref: ./components/requests/products/id/ban/put.request.yml
    UploadFileRequest:
      $ref: ./components/requests/uploads/put.request.yml
    UserUpdateRequest:
      $ref: ./components/requests/users/id/put.request.yml
    UserStatusUpdateRequest:
      $ref: ./components/requests/users/id/status.request.yml
    PublishCommissionSalesBranchRequest:
      $ref: ./components/requests/master/commission-sales-branches/id/publish/post.request.yml
    PublishTransferFeesBranchRequest:
      $ref: ./components/requests/master/transfer-fees-branches/id/publish/post.request.yml
    PublishPurchaseFeesBranchRequest:
      $ref: ./components/requests/master/purchase-fees-branches/id/publish/post.request.yml
    BanOrderRequest:
      $ref: ./components/requests/orders/ban/post.request.yml
    # Response
    ErrorResponse:
      $ref: ./components/responses/error.yml
    AuthResponse:
      $ref: ./components/responses/auth/post.response.yml
    SearchUsersResponse:
      $ref: ./components/responses/users/search/users.response.yml
    SearchBannersResponse:
      $ref: ./components/responses/banners/search/post.response.yml
    SearchProductsResponse:
      $ref: ./components/responses/products/search/post.response.yml
    SellerProductsResponse:
      $ref: ./components/responses/sellers/products/get.response.yml
    UploadFileResponse:
      $ref: ./components/responses/uploads/put.response.yml
    GetListEmailResponse:
      $ref: ./components/responses/users/emails/emails.response.yml
    GetNotificationsResponse:
      $ref: ./components/responses/cms/notifications/get.response.yml
    PublishCustomNGWordsBranchRequest:
      $ref: ./components/requests/master/custom-ng-words-branches/id/publish/post.request.yml
    GetReportsResponse:
      $ref: ./components/responses/reports/get.response.yml
    PublishSubscriptionFeesBranchRequest:
      $ref: ./components/requests/master/subscription-fees-branches/id/publish/post.request.yml
    GetInquiryStatsAreaServiceResponse:
      $ref: ./components/responses/inquiries/stats/area-service/get.response.yml
    GetOrderItemResponse:
      $ref: ./components/responses/order/order-item.response.yml
    GetUserByIdResponse:
      $ref: ./components/responses/users/id/get.response.yml
    GetOrderResponse:
      $ref: ./components/responses/order/order.response.yml
    GetOrderDetailsResponse:
      $ref: ./components/responses/order/order-details.response.yml
    GetSellerProfileResponse:
      $ref: ./components/responses/sellers/id/get.response.yml
    CreateUserNotificationRequest:
      $ref: ./components/requests/users/id/notifications/post.request.yml
    GetCountriesResponse:
      $ref: ./components/responses/countries/get.response.yml
    GlobalUserSummaryResponse:
      $ref: ./components/responses/user-statistics/global-summary/get.response.yml
    CountryUserSummaryResponse:
      $ref: ./components/responses/user-statistics/country-summary/get.response.yml
    RegionUserSummaryResponse:
      $ref: ./components/responses/user-statistics/region-summary/get.response.yml
    
    # Order Statistics Models
    OrderStatistics:
      $ref: ./components/models/order-statistics/order-statistics.yml
    SellerRevenue:
      $ref: ./components/models/order-statistics/seller-revenue.yml
    SellerTax:
      $ref: ./components/models/order-statistics/seller-tax.yml
    MarketplaceVAT:
      $ref: ./components/models/order-statistics/marketplace-vat.yml
    VatObligation:
      $ref: ./components/models/order-statistics/vat-obligation.yml
    PlatformFees:
      $ref: ./components/models/order-statistics/platform-fees.yml
    CountryBreakdown:
      $ref: ./components/models/order-statistics/country-breakdown.yml
    RegionBreakdown:
      $ref: ./components/models/order-statistics/region-breakdown.yml
    OrderStatisticsResponse:
      $ref: ./components/responses/order-statistics/get.response.yml
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
