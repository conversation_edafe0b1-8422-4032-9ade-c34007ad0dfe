summary: Get region user summary
description: Retrieve user statistics for a specific region with sub-region breakdown
operationId: get-region-user-summary
tags:
  - user-statistics
security:
  - bearerAuth: []
parameters:
  - name: regionId
    in: query
    required: true
    description: Unique identifier for the region
    schema:
      type: string
      example: "region_123"
responses:
  200:
    description: Success
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/RegionUserSummaryResponse
  400:
    description: Bad Request (Validation Error)
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  401:
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  403:
    description: Forbidden
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  404:
    description: Region Not Found
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  500:
    description: Internal Server Error
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  503:
    description: Service Unavailable
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
