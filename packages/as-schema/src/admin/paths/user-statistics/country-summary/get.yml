summary: Get country user summary
description: Retrieve user statistics for a specific country with region breakdown
operationId: get-country-user-summary
tags:
  - user-statistics
security:
  - bearerAuth: []
parameters:
  - name: countryCode
    in: query
    required: true
    description: ISO3 country code (e.g., "USA", "JPN")
    schema:
      type: string
      example: "USA"
responses:
  200:
    description: Success
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/CountryUserSummaryResponse
  400:
    description: Bad Request (Validation Error)
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  401:
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  403:
    description: Forbidden
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  404:
    description: Country Not Found
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  500:
    description: Internal Server Error
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  503:
    description: Service Unavailable
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
