summary: Get global user summary
description: Retrieve overall user statistics across all countries with country breakdown
operationId: get-global-user-summary
tags:
  - user-statistics
security:
  - bearerAuth: []
responses:
  200:
    description: Success
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/GlobalUserSummaryResponse
  400:
    description: Bad Request (Validation Error)
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  401:
    description: Unauthorized
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  403:
    description: Forbidden
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  500:
    description: Internal Server Error
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
  503:
    description: Service Unavailable
    content:
      application/json:
        schema:
          $ref: ../../../index.yml#/components/schemas/ErrorResponse
