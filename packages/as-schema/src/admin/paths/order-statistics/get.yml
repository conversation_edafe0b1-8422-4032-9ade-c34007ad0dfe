operationId: get-order-statistics
summary: Get Order Statistics
description: Retrieve comprehensive order statistics including seller revenue analytics, marketplace VAT obligations, platform fees, and geographical breakdowns for a specific month and year.
tags:
  - order-statistics
parameters:
  - name: year
    in: query
    required: true
    description: The year for statistics (e.g., 2024)
    schema:
      type: integer
      example: 2024
  - name: month
    in: query
    required: true
    description: The month for statistics (1-12)
    schema:
      type: integer
      minimum: 1
      maximum: 12
      example: 3
  - name: countryCode
    in: query
    required: false
    description: ISO3 country code for filtering (e.g., "USA", "JPN")
    schema:
      type: string
      pattern: '^[A-Z]{3}$'
      example: "USA"
  - name: regionId
    in: query
    required: false
    description: Region ID for geographical filtering
    schema:
      type: string
      example: "region_001"
security:
  - bearerAuth: []
responses:
  200:
    description: Order statistics retrieved successfully
    content:
      application/json:
        schema:
          $ref: ../../index.yml#/components/schemas/OrderStatisticsResponse
        examples:
          march_2024_stats:
            summary: March 2024 Order Statistics
            value:
              sellerRevenue:
                orderCount: 1250
                totalOrderAmount: 125000.50
                totalSalesAmount: 115000.25
                totalProductPrice: 100000.00
                sellerTaxes:
                  - taxAmount: 8000.00
                    taxRate: 8.0
                  - taxAmount: 2000.25
                    taxRate: 10.0
              marketplaceVAT:
                vatObligations:
                  - amount: 50000.00
                    taxAmount: 4000.00
                    taxRate: 8.0
                  - amount: 15000.00
                    taxAmount: 1500.00
                    taxRate: 10.0
              platformFees:
                memberDiscount: 5000.00
                sellingFees: 12500.00
                buyingFees: 8750.00
              countryBreakdown:
                - countryCode: "USA"
                  sellerRevenue:
                    orderCount: 800
                    totalOrderAmount: 80000.00
                    totalSalesAmount: 75000.00
                    totalProductPrice: 65000.00
                    sellerTaxes:
                      - taxAmount: 5200.00
                        taxRate: 8.0
                  marketplaceVAT:
                    vatObligations:
                      - amount: 30000.00
                        taxAmount: 2400.00
                        taxRate: 8.0
                  platformFees:
                    memberDiscount: 3000.00
                    sellingFees: 8000.00
                    buyingFees: 5500.00
                - countryCode: "JPN"
                  sellerRevenue:
                    orderCount: 450
                    totalOrderAmount: 45000.50
                    totalSalesAmount: 40000.25
                    totalProductPrice: 35000.00
                    sellerTaxes:
                      - taxAmount: 2800.00
                        taxRate: 8.0
                      - taxAmount: 2000.25
                        taxRate: 10.0
                  marketplaceVAT:
                    vatObligations:
                      - amount: 20000.00
                        taxAmount: 1600.00
                        taxRate: 8.0
                  platformFees:
                    memberDiscount: 2000.00
                    sellingFees: 4500.00
                    buyingFees: 3250.00
              regionBreakdown:
                - regionId: "region_001"
                  regionName: "North America"
                  sellerRevenue:
                    orderCount: 800
                    totalOrderAmount: 80000.00
                    totalSalesAmount: 75000.00
                    totalProductPrice: 65000.00
                    sellerTaxes:
                      - taxAmount: 5200.00
                        taxRate: 8.0
                  marketplaceVAT:
                    vatObligations:
                      - amount: 30000.00
                        taxAmount: 2400.00
                        taxRate: 8.0
                  platformFees:
                    memberDiscount: 3000.00
                    sellingFees: 8000.00
                    buyingFees: 5500.00
  400:
    description: Bad Request - Invalid parameters
    content:
      application/json:
        schema:
          $ref: ../../index.yml#/components/schemas/ErrorResponse
        examples:
          invalid_year:
            summary: Invalid year parameter
            value:
              error: "Invalid year parameter. Must be between 2020 and 2030"
              code: "INVALID_YEAR"
          invalid_month:
            summary: Invalid month parameter
            value:
              error: "Invalid month parameter. Must be between 1 and 12"
              code: "INVALID_MONTH"
          invalid_country_code:
            summary: Invalid country code format
            value:
              error: "Invalid country code format. Must be 3 uppercase letters"
              code: "INVALID_COUNTRY_CODE"
  401:
    description: Unauthorized - Invalid or missing authentication token
    content:
      application/json:
        schema:
          $ref: ../../index.yml#/components/schemas/ErrorResponse
  403:
    description: Forbidden - Insufficient permissions for admin access
    content:
      application/json:
        schema:
          $ref: ../../index.yml#/components/schemas/ErrorResponse
  404:
    description: Not Found - No data available for the specified parameters
    content:
      application/json:
        schema:
          $ref: ../../index.yml#/components/schemas/ErrorResponse
        examples:
          no_data:
            summary: No data found
            value:
              error: "No order statistics found for the specified year and month"
              code: "NO_DATA_FOUND"
  500:
    description: Internal Server Error
    content:
      application/json:
        schema:
          $ref: ../../index.yml#/components/schemas/ErrorResponse
  503:
    description: Service Unavailable
    content:
      application/json:
        schema:
          $ref: ../../index.yml#/components/schemas/ErrorResponse
