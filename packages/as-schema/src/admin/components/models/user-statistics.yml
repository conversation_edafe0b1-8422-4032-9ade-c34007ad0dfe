type: object
description: User statistics summary containing aggregated data
required:
  - totalUsers
  - activeUsers
  - sellerUsers
  - premiumUsers
  - generalUsers
properties:
  displayName:
    type: object
    additionalProperties:
      type: string
    description: Display name
    example: {
      "en": "United States",
    }
  totalUsers:
    type: integer
    description: Total number of users
    example: 15000
  activeUsers:
    type: integer
    description: Number of active users
    example: 12000
  sellerUsers:
    type: integer
    description: Number of seller users
    example: 500
  premiumUsers:
    type: integer
    description: Number of premium users
    example: 2000
  generalUsers:
    type: integer
    description: Number of general users
    example: 12500
  countryBreakdown:
    type: array
    description: Statistics breakdown by country (only populated for global summary)
    items:
      $ref: ../../index.yml#/components/schemas/CountryStatistics
  regionBreakdown:
    type: array
    description: Statistics breakdown by region (only populated for country/region summaries)
    items:
      $ref: ../../index.yml#/components/schemas/RegionStatistics
