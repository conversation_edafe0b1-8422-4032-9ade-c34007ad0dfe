type: object
description: Region breakdown statistics
required:
  - regionId
  - regionName
  - sellerRevenue
  - marketplaceVAT
  - platformFees
properties:
  regionId:
    type: string
    description: Region ID
    example: "region_001"
  displayName:
    type: object
    additionalProperties:
      type: string
    description: Region display name
    example: {
      "en": "United States",
    }
  sellerRevenue:
    $ref: ../../../index.yml#/components/schemas/SellerRevenue
  marketplaceVAT:
    $ref: ../../../index.yml#/components/schemas/MarketplaceVAT
  platformFees:
    $ref: ../../../index.yml#/components/schemas/PlatformFees
