type: object
description: Comprehensive order statistics data
required:
  - sellerRevenue
  - marketplaceVAT
  - platformFees
  - countryBreakdown
  - regionBreakdown
properties:
  sellerRevenue:
    $ref: ../../../index.yml#/components/schemas/SellerRevenue
  marketplaceVAT:
    $ref: ../../../index.yml#/components/schemas/MarketplaceVAT
  platformFees:
    $ref: ../../../index.yml#/components/schemas/PlatformFees
  countryBreakdown:
    type: array
    description: Statistics broken down by country
    items:
      $ref: ../../../index.yml#/components/schemas/CountryBreakdown
  regionBreakdown:
    type: array
    description: Statistics broken down by region
    items:
      $ref: ../../../index.yml#/components/schemas/RegionBreakdown
