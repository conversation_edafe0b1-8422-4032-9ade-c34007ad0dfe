type: object
description: Country breakdown statistics
required:
  - countryCode
  - sellerRevenue
  - marketplaceVAT
  - platformFees
properties:
  countryCode:
    type: string
    description: ISO3 country code
    pattern: '^[A-Z]{3}$'
    example: "USA"
  displayName:
    type: object
    additionalProperties:
      type: string
    description: Country display name
    example: {
      "en": "United States",
    }
  sellerRevenue:
    $ref: ../../../index.yml#/components/schemas/SellerRevenue
  marketplaceVAT:
    $ref: ../../../index.yml#/components/schemas/MarketplaceVAT
  platformFees:
    $ref: ../../../index.yml#/components/schemas/PlatformFees
