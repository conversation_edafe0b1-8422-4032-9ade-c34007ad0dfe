type: object
description: Platform fees data
required:
  - memberDiscount
  - sellingFees
  - buyingFees
properties:
  memberDiscount:
    type: number
    format: double
    description: Total member discounts applied
    minimum: 0
    example: 5000.00
  sellingFees:
    type: number
    format: double
    description: Selling fees including taxes
    minimum: 0
    example: 12500.00
  buyingFees:
    type: number
    format: double
    description: Buying fees including taxes
    minimum: 0
    example: 8750.00
