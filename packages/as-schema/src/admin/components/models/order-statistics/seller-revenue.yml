type: object
description: Seller revenue analytics data
required:
  - orderCount
  - totalOrderAmount
  - totalSalesAmount
  - totalProductPrice
  - sellerTaxes
properties:
  orderCount:
    type: integer
    description: Total number of orders
    minimum: 0
    example: 1250
  totalOrderAmount:
    type: number
    format: double
    description: Total price of all orders from seller
    minimum: 0
    example: 125000.50
  totalSalesAmount:
    type: number
    format: double
    description: Confirmed revenue including taxes for seller
    minimum: 0
    example: 115000.25
  totalProductPrice:
    type: number
    format: double
    description: Total price of products sold by seller
    minimum: 0
    example: 100000.00
  sellerTaxes:
    type: array
    description: Taxes that seller must pay, grouped by tax rate
    items:
      $ref: ../../../index.yml#/components/schemas/SellerTax
