type: object
description: User statistics for a specific region
required:
  - regionId
  - totalUsers
  - activeUsers
  - sellerUsers
  - premiumUsers
  - generalUsers
properties:
  regionId:
    type: string
    description: Unique identifier for the region
    example: "region_123"
  displayName:
    type: object
    additionalProperties:
      type: string
    description: Region display name
    example: {
      "en": "United States",
    }
  totalUsers:
    type: integer
    description: Total number of users in this region
    example: 1000
  activeUsers:
    type: integer
    description: Number of active users in this region
    example: 800
  sellerUsers:
    type: integer
    description: Number of seller users in this region
    example: 50
  premiumUsers:
    type: integer
    description: Number of premium users in this region
    example: 150
  generalUsers:
    type: integer
    description: Number of general users in this region
    example: 800
