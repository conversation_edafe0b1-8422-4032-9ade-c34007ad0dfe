type: object
description: User statistics for a specific country
required:
  - country
  - totalUsers
  - activeUsers
  - sellerUsers
  - premiumUsers
  - generalUsers
properties:
  country:
    type: string
    description: ISO3 country code
    example: "USA"
  displayName:
    type: object
    additionalProperties:
      type: string
    description: Country display name
    example: {
      "en": "United States",
    }
  totalUsers:
    type: integer
    description: Total number of users in this country
    example: 5000
  activeUsers:
    type: integer
    description: Number of active users in this country
    example: 4000
  sellerUsers:
    type: integer
    description: Number of seller users in this country
    example: 200
  premiumUsers:
    type: integer
    description: Number of premium users in this country
    example: 800
  generalUsers:
    type: integer
    description: Number of general users in this country
    example: 4000
