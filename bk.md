### Hierarchical Data Summary by Country/Region — Design Specification

#### Objective
Provide a scalable, flexible, and efficient data summary feature with three aggregation levels:
- Global Summary
- Country Summary
- Region Summary (supports nested sub-regions of arbitrary depth)

This design follows existing marketplace service patterns (Clean Architecture) and database conventions in `packages/as-api/`, and OpenAPI patterns in `packages/as-schema/`.

---

### Terminology and Hierarchy Model
- **Geo Node**: A geographic entity in a tree (Global → Country → Region → Sub-region → …).
- **Country**: ISO-3166 alpha-2 code as canonical identifier (e.g., JP, US).
- **Region/Sub-region**: Arbitrary hierarchy depth within a country (e.g., Prefecture → City → Ward).
- **Unassigned**: Bucket for records that cannot be associated with any geo node.

Hierarchy properties:
- Each country has 0..N regions; each region has 0..N sub-regions.
- Parent-child relationships form a tree per country; all trees attach to a virtual Global root.
- Aggregation rolls up from leaves to root using parent-child relationships.

---

### Metrics Scope and Definitions
All metrics are computed per time grain (daily by default), per geo node. The feature returns time-windowed aggregates.

User statistics:
- Total users count: distinct users associated to the geo node.
- Active users count: distinct users with any `user_fcm_tokens.last_use_at >= now() - interval '30 days'`.
- Regular users count: users where `membership != 1` OR user is a seller (excluded from premium set).
- Premium users count: users where `membership = 1` AND `seller_id IS NULL`.
- Seller users count: users where `seller_id IS NOT NULL`.

Order statistics:
- Total orders count.
- Total order value (pre-tax).
- Total order value (post-tax).
- Total product value.
- Total tax amount.
- Total member discounts applied.
- Total purchase fees.
- Total selling fees.

Notes:
- A user is counted once per geo node per period after association logic (see Data Association). Active uses user-level deduplication.
- Orders are counted by order-level association; monetary values sum over associated orders.

---

### Data Association (Geo Binding)
Because users and orders may not always have explicit geo fields, define robust association rules:

User → Geo:
1) If `user_profiles.region_id` is present ⇒ bind to that geo node.
2) Else if `user_profiles.country_code` present ⇒ bind to country node.
3) Else infer from most recent order’s shipping address ⇒ derive region (fallback to country).
4) Else bind to `UNASSIGNED` node under Global.

Order → Geo:
1) If order has `shipping_address.region_id` ⇒ bind to that region.
2) Else if `shipping_address.country_code` ⇒ bind to country.
3) Else if seller’s fulfillment location is known ⇒ bind to that geo (configurable flag: prefer buyer vs seller geo; default buyer).
4) Else bind to `UNASSIGNED` under Global.

Implementation detail: maintain deterministic mapping logic in one service to ensure consistent attribution across subsystems.

---

### Data Model

Dimensions:
- `dim_geo`
  - `geo_id` (PK, bigint)
  - `parent_geo_id` (nullable, references `dim_geo.geo_id`)
  - `geo_level` enum: GLOBAL | COUNTRY | REGION
  - `country_code` (nullable for non-country levels)
  - `name`
  - `path` (materialized path, e.g., /GLOBAL/JP/Tokyo/Shinjuku)
  - `is_unassigned` boolean (true for the special Unassigned child)
  - Unique constraints: (country_code, name, parent_geo_id) for REGION levels

- `dim_date`
  - Standard calendar dimension for filtering/rollups.

Fact tables (append-only, partitioned by date):
- `fact_user_daily`
  - `date_id` → `dim_date`
  - `geo_id` → `dim_geo`
  - `user_id`
  - `is_active_30d` boolean
  - `is_premium_non_seller` boolean
  - `is_seller` boolean
  - Storage: one row per user per day per geo_id (incremental snapshot), OR store only deltas and aggregate with views (see Performance).

- `fact_order_daily`
  - `date_id` → `dim_date`
  - `geo_id` → `dim_geo`
  - `order_id`
  - `total_value_pre_tax` numeric
  - `total_value_post_tax` numeric
  - `product_value` numeric
  - `tax_amount` numeric
  - `member_discount_amount` numeric
  - `purchase_fee_amount` numeric
  - `selling_fee_amount` numeric

Materialized views (per aggregation level & period):
- `mv_geo_user_daily` (geo_id, date_id, totals)
- `mv_geo_order_daily` (geo_id, date_id, sums)
- Optional monthly variants for faster coarse queries.

Indexes/Partitioning:
- Partition `fact_user_daily` and `fact_order_daily` by range on `date_id`.
- Index `(geo_id, date_id)`; bitmap or partial indexes for booleans if needed.

---

### Aggregation Logic

Roll-up across hierarchy via recursive CTE or closure table. Recommended options:
- Option A (default): adjacency list with recursive CTE for on-demand rollups.
- Option B: maintain a closure table `dim_geo_closure(ancestor_geo_id, descendant_geo_id, depth)` for faster aggregation.

Example daily user aggregation (active users) using closure table:
```sql
SELECT
  c.ancestor_geo_id AS geo_id,
  fud.date_id,
  COUNT(DISTINCT fud.user_id) FILTER (WHERE fud.is_active_30d) AS active_users,
  COUNT(DISTINCT fud.user_id) AS total_users,
  COUNT(DISTINCT fud.user_id) FILTER (WHERE fud.is_premium_non_seller) AS premium_users,
  COUNT(DISTINCT fud.user_id) FILTER (WHERE fud.is_seller) AS seller_users,
  COUNT(DISTINCT fud.user_id) FILTER (WHERE NOT fud.is_premium_non_seller AND NOT fud.is_seller) AS regular_users
FROM fact_user_daily fud
JOIN dim_geo_closure c ON c.descendant_geo_id = fud.geo_id
WHERE fud.date_id BETWEEN $1 AND $2
GROUP BY 1,2;
```

Example daily order aggregation:
```sql
SELECT
  c.ancestor_geo_id AS geo_id,
  fod.date_id,
  COUNT(DISTINCT fod.order_id) AS total_orders,
  SUM(fod.total_value_pre_tax) AS total_value_pre_tax,
  SUM(fod.total_value_post_tax) AS total_value_post_tax,
  SUM(fod.product_value) AS total_product_value,
  SUM(fod.tax_amount) AS total_tax_amount,
  SUM(fod.member_discount_amount) AS total_member_discounts,
  SUM(fod.purchase_fee_amount) AS total_purchase_fees,
  SUM(fod.selling_fee_amount) AS total_selling_fees
FROM fact_order_daily fod
JOIN dim_geo_closure c ON c.descendant_geo_id = fod.geo_id
WHERE fod.date_id BETWEEN $1 AND $2
GROUP BY 1,2;
```

Active users computation (derive `is_active_30d`):
```sql
-- Pseudocode: compute most recent activity per user
WITH last_use AS (
  SELECT user_id, MAX(last_use_at) AS last_use_at
  FROM user_fcm_tokens
  GROUP BY user_id
)
SELECT u.user_id,
       (last_use.last_use_at >= NOW() - INTERVAL '30 days') AS is_active_30d
FROM users u
LEFT JOIN last_use ON last_use.user_id = u.id;
```

---

### Data Refresh Strategy
- Near-real-time: event-driven upserts to facts on `order_created`, `order_paid`, `order_updated`, `user_updated`, `fcm_token_touched`.
- Batch: nightly recomputation window to reconcile drifts; refresh materialized views.
- Retention: keep daily partitions for 24 months; archive to cold storage afterwards.

Performance optimizations:
- Partition facts by `date_id`.
- Maintain closure table for O(1) ancestor joins.
- Use materialized views for common windows (7d, 30d, MTD, YTD).
- Cache API responses per key `(scope, geo_id, window, interval, params-hash)`.

---

### API Design (Admin-only; kebab-case paths)
Base: `/admin/analytics`

- GET `/admin/analytics/summary/global`
  - Query: `from`, `to`, `interval=daily|monthly`, `include_unassigned=bool`, `include_breakdown=bool`

- GET `/admin/analytics/summary/countries`
  - Returns one row per country.

- GET `/admin/analytics/summary/countries/{countryCode}`
  - Returns country node aggregate and optional immediate children.

- GET `/admin/analytics/summary/countries/{countryCode}/regions`
  - Returns region list (optionally nested with `depth`).

- GET `/admin/analytics/summary/geo/{geoId}/tree`
  - Query: `depth=int` (default 1), returns node + children with aggregates.

Common query parameters:
- `from` / `to` (ISO date), `timezone`, `interval`, `include_unassigned`, `depth`, `include_breakdown` (user-type and fee-type splits).

Response shape (sketch):
```json
{
  "node": { "geoId": 1, "level": "GLOBAL", "name": "Global" },
  "window": { "from": "2025-01-01", "to": "2025-01-31", "interval": "daily" },
  "metrics": {
    "users": {
      "total": 12345,
      "active30d": 9876,
      "regular": 8000,
      "premium": 1500,
      "seller": 845
    },
    "orders": {
      "count": 5432,
      "valuePreTax": 1234567.89,
      "valuePostTax": 1350000.12,
      "productValue": 1100000.00,
      "tax": 115000.12,
      "memberDiscounts": 25000.00,
      "purchaseFees": 20000.00,
      "sellingFees": 18000.00
    }
  },
  "children": [ { "geoId": 100, "level": "COUNTRY", "name": "JP", "metrics": { /* ... */ } } ]
}
```

OpenAPI conventions:
- Paths in kebab-case under `packages/as-schema/src/api/admin/analytics/*.yml`.
- Use shared schema components for metric blocks; filenames in snake_case.
- Generate clients per existing workflow (do not edit `.gen.go` in server).

---

### Clean Architecture Integration (packages/as-api)

Layers:
- Handler (HTTP): `api/admin/analytics/summary_handler.go`
- Service (domain): `internal/analytics/service.go`
- Repository (DB): `internal/analytics/repository.go`

Responsibilities:
- Handler: validate params, authZ, call service, format response.
- Service: orchestrate time windows, resolve geo nodes, call repos, apply rollups/caching.
- Repository: query materialized views/facts, perform RLS-like filters if needed.

Caching:
- Use existing cache service (`packages/as-api/cache`) with key: `analytics:{geo_id}:{from}:{to}:{interval}:{flags_hash}`.
- TTL: 5–15 minutes (configurable).

Logging/Errors:
- Use `logger.FieldMap` with fields `{ geo_id, window_from, window_to, interval, depth }`.
- Consistent error domain types; return sanitized messages from handlers.

Security:
- Admin-only routes behind existing auth middleware.
- Optional per-country scope checks for region-limited operators.

---

### Repository Query Strategy
- Prefer querying materialized views (`mv_geo_user_daily`, `mv_geo_order_daily`) for most endpoints.
- Fallback to facts for ad-hoc windows not covered by materialized views.
- Use closure table join for hierarchical rollups.

Pagination & Limits:
- Country list: limit 200.
- Region tree: limit breadth per page, `depth` max 5 by default.

---

### Data Ingestion & Maintenance
- Event consumers populate facts on entity changes.
- Nightly job rebuilds closure table from `dim_geo` and refreshes materialized views.
- Geo admin UI (future): manage `dim_geo` tree (countries, regions, sub-regions), with validations.

---

### Edge Cases & Policies
- Multiple FCM tokens per user: use MAX(last_use_at) per user; active if within 30 days.
- Orders without amounts (cancelled/refunded): include only completed/paid states per business rule; maintain state filter in facts load.
- Currency handling: normalize to platform currency before aggregations; store source currency + fx rate in facts if multicurrency.
- Timezone: compute `date_id` in a canonical timezone; adjust windows based on `timezone` param when aggregating.
- Unassigned: always reported as a child under Global and under each country as needed; hide via `include_unassigned=false`.

---

### Performance Considerations
- Partitioned facts, indexed by `(geo_id, date_id)`.
- Closure table reduces recursive overhead for deep trees.
- Materialized views for hot windows; incremental refresh via `CONCURRENTLY` where supported.
- Response caching at service layer.

---

### Testing Strategy
- Repository: golden SQL tests for representative windows and hierarchies.
- Service: hierarchy roll-up tests, active user correctness with multiple tokens.
- Handler: parameter validation, RBAC, pagination, and performance guards.

---

### Implementation Steps (High Level)
1) Create `dim_geo`, optional `dim_geo_closure`, `fact_user_daily`, `fact_order_daily`, `mv_*` objects.
2) Implement geo association service for users/orders.
3) Build repository queries for aggregates (facts + MVs + closure).
4) Implement service orchestration with caching and parameter handling.
5) Implement handlers and add OpenAPI specs under `packages/as-schema`.
6) Add event consumers and nightly refresh jobs.
7) Add tests and dashboards.

---

### Backward/Forward Compatibility
- New geo nodes can be added without breaking existing aggregates; closure table rebuild captures new links.
- Facts are append-only; reprocessing can backfill windows safely.
- API contracts versioned under `/admin/analytics` with schema components to allow non-breaking extensions.

---

### Notes for Operations
- Run all DB migrations and code generation via Docker per QUICK_REFERENCE.
- Never edit generated files; place new code under appropriate layers.
- Monitor job durations and MV refresh times; scale partitions and indexes as data grows.


